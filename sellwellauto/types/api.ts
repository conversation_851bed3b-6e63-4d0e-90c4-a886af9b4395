// API响应基础类型
export interface ApiResponse<T = unknown> {
  code: number
  message: string
  data: T
}

// 新闻相关类型
export interface NewsItem {
  id: number
  label: string
  image: string
  title: string
  content: string
  createdAt: string
  detail_url: string
}

export interface NewsListResponse {
  data: NewsItem[]
  meta: {
    count: number
    page: number
    pageSize: number
  }
}

export interface NewsDetailResponse {
  data: NewsItem
}

// 分页参数类型
export interface PaginationParams {
  page: number
  pageSize: number
}

// 新闻查询参数
export interface NewsQueryParams extends PaginationParams {
  category?: string
  keyword?: string
}
