// 用户相关类型
export interface User {
  id: number
  username: string
  account?: string
  nickname?: string
  f_belong_company?: string
  token?: string
  role?: string
  email?: string
  status?: string
  createdAt?: string
  updatedAt?: string
}

export interface UserInfo {
  id: number
  username: string
  account: string
  nickname: string
  f_belong_company: string
}

// API响应类型
export interface ApiResponse<T = unknown> {
  data: T
  message?: string
  code?: number
  success?: boolean
}

// 订单相关类型
export interface Order {
  id: number
  createdBy: number
  status: string
  createdAt: string
  updatedAt: string
  [key: string]: unknown
}

// 短信验证码类型
export interface SmsCodeParams {
  action: string
  verifier: string
  uuid: string
}

// 登录参数类型
export interface LoginParams {
  uuid: string
  code: string
}
