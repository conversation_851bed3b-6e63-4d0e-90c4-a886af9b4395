declare module '#app' {
  interface NuxtApp {
    $i18n: {
      locale: string
      locales: Array<{
        code: string
        iso: string
        name: string
        file: string
      }>
    }
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $t: (key: string) => string
    $i18n: {
      locale: string
      locales: Array<{
        code: string
        iso: string
        name: string
        file: string
      }>
    }
  }
}

export {}
