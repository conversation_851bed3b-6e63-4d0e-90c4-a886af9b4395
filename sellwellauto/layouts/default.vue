<template>
  <div class="app">
    <!-- 应用头部 -->
    <AppHeader v-if="!isExcludedPage" />

    <!-- 主要内容区 -->
    <main class="main-content" :class="{ 'no-header': isExcludedPage }">
      <slot />
    </main>

    <!-- 联系我们区域（底部） -->
    <ContactSection v-if="!isExcludedPage" />

    <!-- 侧边栏 -->
    <Sidebar v-if="!isExcludedPage" />
  </div>
</template>

<script setup lang="ts">
// 排除显示头部和底部的页面
const excludedPages = ['/login', '/404']
const route = useRoute()

// 判断是否为排除页面
const isExcludedPage = computed(() => {
  return excludedPages.includes(route.path)
})

// SEO设置
useHead({
  titleTemplate: '%s - 赛沃车联 Sellwell Auto',
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    {
      name: 'description',
      content: '赛沃车联专注于二手车出口，连接车源方、外贸商、海外买家，让出口更简单、更高效',
    },
  ],
})
</script>

<style lang="scss" scoped>
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  margin-top: 64px; // 头部高度
}

.main-content.no-header {
  margin-top: 0; // 登录页面等不显示头部的页面
}
</style>
