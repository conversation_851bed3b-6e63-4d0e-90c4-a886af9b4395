import { defineEvent<PERSON><PERSON><PERSON>, getCookie, setCookie } from 'h3'
import { detectRegionAndLocale } from '../utils/region'

// 只在服务器端运行，用于根据请求头/地区初始化语言
export default defineEventHandler(async (event) => {
  // 跳过静态资源与内部API健康检查等
  const url = event.node.req.url || ''
  if (
    url.startsWith('/_nuxt') ||
    url.startsWith('/assets') ||
    url.startsWith('/public') ||
    url.startsWith('/favicon') ||
    url.startsWith('/__')
  ) {
    return
  }

  // 支持语言列表与后备语言，与 nuxt.config.ts 保持一致
  const supported = ['zh-CN', 'en']
  const fallback = 'en'

  // 如果已有 i18n_redirected 则不覆盖（尊重用户选择）
  const existing = getCookie(event, 'i18n_redirected')
  if (existing) {
    // 使用用户的 Cookie 偏好进行检测
    const result = await detectRegionAndLocale(
      event,
      supported,
      fallback,
      existing
    )
    ;(event as any).context = (event as any).context || {}
    ;(event as any).context.region = result
    setCookie(event, 'i18n_redirected', result.finalLocale, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365,
      sameSite: 'lax'
    })

    console.log(
      `[region] 尊重用户选择 url=${url} cookieLocale=${existing} ip=${result.ip} country=${result.country || '-'} finalLocale=${result.finalLocale} reason=${result.reason}`
    )

    return
  }

  const result = await detectRegionAndLocale(event, supported, fallback, null)
  console.log(
    `[region] 新用户 url=${url} ip=${result.ip} country=${result.country || '-'} finalLocale=${result.finalLocale} reason=${result.reason}`
  )

  // 使用直接设置响应头的方式，确保 Cookie 被正确设置
  setCookie(event, 'i18n_redirected', result.finalLocale, {
    path: '/',
    maxAge: 60 * 60 * 24 * 365,
    sameSite: 'lax'
  })

  // 将解析结果挂载到请求上下文，供 API/页面调试使用
  ;(event as any).context = (event as any).context || {}
  ;(event as any).context.region = result
})
