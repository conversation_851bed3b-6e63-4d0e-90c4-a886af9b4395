import type { H3Event } from 'h3'
import { getHeader } from 'h3'

// 简单的IP解析：优先 X-Forwarded-For，其次 cf-connecting-ip，最后 remoteAddress
function getClientIp(event: H3Event): string {
  const forwarded = getHeader(event, 'x-forwarded-for') || ''
  const cfIp = getHeader(event, 'cf-connecting-ip') || ''
  const remoteRaw =
    event.node &&
    event.node.req &&
    event.node.req.socket &&
    event.node.req.socket.remoteAddress
  const remote = typeof remoteRaw === 'string' ? remoteRaw : ''

  if (forwarded) {
    // 可能包含多个以逗号分隔，取第一个
    const primary = forwarded.split(',')[0]?.trim() || ''
    if (primary) return primary
  }
  if (cfIp) return cfIp
  return remote
}

// 简单UA推测（备用）
function guessLocaleByUA(ua: string): string | null {
  const lc = ua.toLowerCase()
  if (lc.includes('zh-cn') || lc.includes('zh-hans') || lc.includes('zh'))
    return 'zh-CN' // 中文
  if (lc.includes('en')) return 'en' // 英文
  return null
}

// 基于国家/地区映射语言
function mapCountryToLocale(countryCode?: string | null): string | null {
  if (!countryCode) return null
  const cc = countryCode.toUpperCase()
  // 中国大陆及港澳台地区使用中文
  if (['CN', 'HK', 'MO', 'TW'].includes(cc)) return 'zh-CN'
  // 其他地区使用英文
  return 'en'
}

// 从Cloudflare / Vercel 等边缘头部推断地区
function getCountryFromHeaders(event: H3Event): string | null {
  // Cloudflare-Country, cf-ipcountry, x-vercel-ip-country, fly-client-ip-country 等常见头
  const headers = [
    'cf-ipcountry',
    'cloudflare-country',
    'x-vercel-ip-country',
    'x-forwarded-country',
    'x-geo-country'
  ]
  for (const key of headers) {
    const v = getHeader(event, key)
    if (v) return v
  }
  return null
}

// 通过 IP 查询地理位置 - 主接口
async function getCountryFromIPInfo(ip: string): Promise<string | null> {
  try {
    // 使用 ipinfo.io (免费版本无限制请求，支持 HTTPS)
    const response = await fetch(`https://ipinfo.io/${ip}/json`)
    const data = await response.json()
    console.log('[IP查询] ipinfo.io 结果:', data)
    return data.country || null
  } catch (error) {
    console.log('[IP查询] ipinfo.io 失败:', error)
    return null
  }
}

// 通过 IP 查询地理位置 - 带备用方案
async function getCountryFromIP(ip: string): Promise<string | null> {
  // 首先尝试 ipinfo.io
  // const country = await getCountryFromIPInfo(ip)
  const country = ''

  if (country) {
    console.log(`[IP查询] 最终结果: ${ip} -> ${country}`)
  } else {
    console.warn(`[IP查询] 接口失败: ${ip}`)
  }

  return country
}

export interface RegionDetectResult {
  ip: string
  country: string | null
  localeFromCountry: string | null
  localeFromUA: string | null
  finalLocale: string
  reason: string
}

export async function detectRegionAndLocale(
  event: H3Event,
  supported: string[],
  fallback: string,
  preferredLocale?: string | null
): Promise<RegionDetectResult> {
  const ip = getClientIp(event)
  console.log('用户IP：', ip)
  let country = getCountryFromHeaders(event)

  // 如果头部没有地区信息，通过 IP 查询
  if (!country && ip && ip !== '::ffff:127.0.0.1') {
    country = await getCountryFromIP(ip)
  }

  const ua = getHeader(event, 'user-agent') || ''
  const uaLocale = guessLocaleByUA(ua)
  const countryLocale = mapCountryToLocale(country)

  let finalLocale = fallback
  let reason = 'fallback'

  // 如果用户有明确的语言偏好，优先使用
  if (preferredLocale && supported.includes(preferredLocale)) {
    finalLocale = preferredLocale
    reason = 'user_preference'
  }
  // 否则根据国家码来确定
  else if (countryLocale === 'zh-CN') {
    finalLocale = 'zh-CN'
    reason = 'country_CN'
  } else if (countryLocale === 'en') {
    finalLocale = 'en'
    reason = 'country_EN'
  } else {
    finalLocale = fallback
    reason = 'fallback'
  }

  return {
    ip,
    country: country || null,
    localeFromCountry: countryLocale,
    localeFromUA: uaLocale,
    finalLocale,
    reason
  }
}
