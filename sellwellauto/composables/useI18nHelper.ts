export const useI18nHelper = () => {
  const { locale, locales, t, setLocale } = useI18n()

  // 获取当前语言信息
  const currentLanguage = computed(() => {
    return locales.value.find((l) => l.code === locale.value)
  })

  // 获取所有可用语言
  const availableLanguages = computed(() => {
    return locales.value.map((l) => ({
      code: l.code,
      name: l.name,
      iso: l.iso,
      flag: (l as any).flag || '' // 支持国旗emoji
    }))
  })

  // 切换语言（支持动态语言代码）
  const changeLanguage = async (langCode: any) => {
    if (locale.value === langCode) return

    // 验证语言代码是否支持
    const supportedLocale = locales.value.find((l) => l.code === langCode)
    if (!supportedLocale) {
      console.warn(`不支持的语言: ${langCode}`)
      return
    }

    try {
      // 设置语言
      locale.value = langCode as any
      setLocale(langCode)

      // 设置cookie确保语言选择被保存
      const cookie = useCookie('i18n_redirected', {
        default: () => 'zh-CN',
        maxAge: 60 * 60 * 24 * 365, // 1年
        path: '/'
      })
      cookie.value = langCode
    } catch (error) {
      console.error('切换语言失败:', error)
    }
  }

  // 获取翻译文本（带默认值）
  const getText = (key: string, defaultValue: string = '') => {
    const translated = t(key)
    return translated !== key ? translated : defaultValue
  }

  // 格式化日期（根据当前语言）
  const formatDate = (date: Date | string, _format: string = 'YYYY-MM-DD') => {
    const d = new Date(date)
    const currentLocale = locale.value as string

    // 根据语言设置不同的日期格式
    switch (currentLocale) {
      case 'zh-CN':
        return `${d.getFullYear()}年${d.getMonth() + 1}月${d.getDate()}日`
      case 'ja':
        return `${d.getFullYear()}年${d.getMonth() + 1}月${d.getDate()}日`
      case 'ko':
        return `${d.getFullYear()}년 ${d.getMonth() + 1}월 ${d.getDate()}일`
      case 'en':
        return d.toLocaleDateString('en-US')
      case 'es':
        return d.toLocaleDateString('es-ES')
      case 'fr':
        return d.toLocaleDateString('fr-FR')
      case 'de':
        return d.toLocaleDateString('de-DE')
      case 'ru':
        return d.toLocaleDateString('ru-RU')
      case 'ar':
        return d.toLocaleDateString('ar-SA')
      default:
        return d.toLocaleDateString('en-US')
    }
  }

  // 获取数字格式化（根据当前语言）
  const formatNumber = (num: number, options?: Intl.NumberFormatOptions) => {
    const localeMap: Record<string, string> = {
      'zh-CN': 'zh-CN',
      en: 'en-US',
      ja: 'ja-JP',
      ko: 'ko-KR',
      es: 'es-ES',
      fr: 'fr-FR',
      de: 'de-DE',
      ru: 'ru-RU',
      ar: 'ar-SA'
    }

    const currentLocale = locale.value as string
    const mappedLocale = localeMap[currentLocale] || 'en-US'
    return new Intl.NumberFormat(mappedLocale, options).format(num)
  }

  // 获取货币格式化（根据当前语言）
  const formatCurrency = (
    amount: number,
    currency: string = 'USD',
    options?: Intl.NumberFormatOptions
  ) => {
    const localeMap: Record<string, string> = {
      'zh-CN': 'zh-CN',
      en: 'en-US',
      ja: 'ja-JP',
      ko: 'ko-KR',
      es: 'es-ES',
      fr: 'fr-FR',
      de: 'de-DE',
      ru: 'ru-RU',
      ar: 'ar-SA'
    }

    const currentLocale = locale.value as string
    const mappedLocale = localeMap[currentLocale] || 'en-US'
    return new Intl.NumberFormat(mappedLocale, {
      style: 'currency',
      currency,
      ...options
    }).format(amount)
  }

  // 检查语言是否支持RTL（从右到左）
  const isRTL = computed(() => {
    const rtlLanguages = ['ar', 'he', 'fa', 'ur']
    return rtlLanguages.includes(locale.value as string)
  })

  // 获取语言方向
  const textDirection = computed(() => {
    return isRTL.value ? 'rtl' : 'ltr'
  })

  return {
    // 状态
    currentLanguage,
    availableLanguages,
    locale: readonly(locale),
    isRTL,
    textDirection,

    // 方法
    changeLanguage,
    getText,
    formatDate,
    formatNumber,
    formatCurrency,
    t
  }
}
