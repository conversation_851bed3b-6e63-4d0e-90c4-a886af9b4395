import type { ApiResponse, User } from '~/types'

// 登录响应类型（继承自 API 返回的用户数据）
export interface LoginResponse {
  token: string
  user: {
    id: number
    username: string
    email?: string
    role?: string
    status?: string
    createdAt?: string
    updatedAt?: string
    account?: string
    nickname?: string
    f_belong_company?: string
  }
  message?: string
}

// 订单相关类型
export interface Order {
  id: number
  orderNo: string
  userId: number
  status: string
  totalAmount: number
  createdAt: string
  updatedAt: string
  [key: string]: unknown
}

// 赛沃车联 ERP API 客户端
export const useSellwellautoApi = () => {
  const config = useRuntimeConfig()

  // 基础请求方法
  const request = async <T>(
    endpoint: string,
    options: {
      method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
      params?: Record<string, unknown>
      body?: unknown
      headers?: Record<string, string>
    } = {}
  ): Promise<T> => {
    const { method = 'GET', params, body, headers = {} } = options

    // 准备请求头
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      'x-locale': 'zh-CN',
      ...headers
    }

    // 计算本地时区，形如 +08:00
    const offsetMinutes = -new Date().getTimezoneOffset()
    const sign = offsetMinutes >= 0 ? '+' : '-'
    const abs = Math.abs(offsetMinutes)
    const hours = String(Math.floor(abs / 60)).padStart(2, '0')
    const minutes = String(abs % 60).padStart(2, '0')
    requestHeaders['x-timezone'] = `${sign}${hours}:${minutes}`

    // 添加认证 token（从 Cookie 读取）
    const tokenCookie = useCookie<string>('token', { default: () => '' })
    if (tokenCookie.value) {
      requestHeaders.Authorization = `Bearer ${tokenCookie.value}`
    }

    // 处理查询参数
    let url = endpoint
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      if (searchParams.toString()) {
        url += (url.includes('?') ? '&' : '?') + searchParams.toString()
      }
    }

    try {
      const response: any = await $fetch<T>(url, {
        baseURL: config.public.sellwellautoApiBase,
        method,
        body: method !== 'GET' ? (body as BodyInit) : undefined,
        headers: requestHeaders
      })
      return response
    } catch (error: unknown) {
      console.error(`赛沃车联 API请求失败 [${method} ${endpoint}]:`, error)
      const errorObj = error as {
        status?: number
        message?: string
        data?: {
          errors?: Array<{ message?: string; code?: string }>
          message?: string
          error?: string
          code?: string
        }
      }

      const status = errorObj.status
      const data = errorObj.data || {}

      // 统一错误信息
      const normalizedMessage =
        data?.errors?.[0]?.message ||
        data?.message ||
        data?.error ||
        errorObj?.message ||
        '请求失败'
      const errorCode = data?.errors?.[0]?.code || data?.code

      // 会话失效拦截：提示并退出登录
      if (errorCode === 'BLOCKED_TOKEN' || status === 401) {
        if (import.meta.client) {
          const userStore = useUserStore()
          userStore.clearUserInfo()
          await navigateTo('/login')
        }
        console.log('normalizedMessage', normalizedMessage)
        throw new Error(normalizedMessage || '您的会话已过期，请重新登录。')
      }

      // 其他错误处理
      if (status === 403) {
        throw new Error('访问被拒绝')
      } else if (status === 404) {
        throw new Error('请求的资源不存在')
      } else if (status && status >= 500) {
        throw new Error('服务器内部错误，请稍后重试')
      }

      // 创建带有标准化消息的错误对象
      const enhancedError = new Error(normalizedMessage)
      ;(enhancedError as any).normalizedMessage = normalizedMessage
      ;(enhancedError as any).status = status

      throw enhancedError
    }
  }

  // 便捷方法
  const get = <T>(
    endpoint: string,
    params?: Record<string, unknown>
  ): Promise<T> => {
    return request<T>(endpoint, { method: 'GET', params })
  }

  const post = <T>(
    endpoint: string,
    body?: unknown,
    params?: Record<string, unknown>
  ): Promise<T> => {
    return request<T>(endpoint, { method: 'POST', body, params })
  }

  const put = <T>(
    endpoint: string,
    body?: unknown,
    params?: Record<string, unknown>
  ): Promise<T> => {
    return request<T>(endpoint, { method: 'PUT', body, params })
  }

  const del = <T>(
    endpoint: string,
    params?: Record<string, unknown>
  ): Promise<T> => {
    return request<T>(endpoint, { method: 'DELETE', params })
  }

  // 发送验证码
  const sendCode = async (data: {
    action: string
    verifier: string
    uuid: string
  }) => {
    return post('/smsOTP:publicCreate', data)
  }

  // 用户认证相关
  const login = async (data: {
    uuid: string
    code: string
  }): Promise<ApiResponse<LoginResponse>> => {
    // 使用短信验证码认证器
    return request<ApiResponse<LoginResponse>>('/auth:signIn', {
      method: 'POST',
      body: data,
      headers: {
        'x-authenticator': 's_lqyjlttpmdv'
      }
    })
  }

  const authSigin = async (data: {
    account: string
    password: string
  }): Promise<ApiResponse<LoginResponse>> => {
    return request<ApiResponse<LoginResponse>>('/auth:signIn', {
      method: 'POST',
      body: data
    })
  }

  const logout = async (): Promise<ApiResponse<null>> => {
    return post<ApiResponse<null>>('/auth/logout')
  }

  const getUserInfo = async (): Promise<ApiResponse<User>> => {
    return get<ApiResponse<User>>('/user/info')
  }

  const refreshToken = async (): Promise<ApiResponse<{ token: string }>> => {
    return post<ApiResponse<{ token: string }>>('/auth/refresh')
  }

  // 订单相关
  const getOrders = async (params: {
    page: number
    pageSize: number
    status?: string
  }): Promise<ApiResponse<Order[]>> => {
    return get<ApiResponse<Order[]>>('/orders', params)
  }

  const getOrderDetail = async (
    orderId: number
  ): Promise<ApiResponse<Order>> => {
    return get<ApiResponse<Order>>(`/orders/${orderId}`)
  }

  const createOrder = async (
    orderData: Omit<Order, 'id' | 'orderNo' | 'createdAt' | 'updatedAt'>
  ): Promise<ApiResponse<Order>> => {
    return post<ApiResponse<Order>>('/orders', orderData)
  }

  const updateOrder = async (
    orderId: number,
    orderData: Partial<Order>
  ): Promise<ApiResponse<Order>> => {
    return put<ApiResponse<Order>>(`/orders/${orderId}`, orderData)
  }

  const deleteOrder = async (orderId: number): Promise<ApiResponse<null>> => {
    return del<ApiResponse<null>>(`/orders/${orderId}`)
  }

  // 其他业务 API 方法可以在这里添加
  const getCommonData = async (
    endpoint: string,
    params?: Record<string, unknown>
  ): Promise<ApiResponse<unknown>> => {
    return get<ApiResponse<unknown>>(endpoint, params)
  }

  // 报价单生成专用API方法
  const createOrders = async (
    data: any,
    triggerFlow?: string
  ): Promise<any> => {
    const params = triggerFlow ? { triggerWorkflows: triggerFlow } : {}
    return post('/orders:create', data, params)
  }

  const getRecommendCars = async (params: {
    ordersId: number
  }): Promise<any> => {
    return get('/recommend_car_source:list', {
      page: 1,
      pageSize: 20,
      filter: `{"$and":[{"orders_id":{"$eq":${params.ordersId}}}]}`
    })
  }

  const updateOrders = async (data: {
    id: number
    automatic_inquiry: string
  }): Promise<any> => {
    return post(`/orders:update?filterByTk=${data.id}`, data)
  }

  // 报价单专用：获取订单明细（与标准的 getOrderDetail 不同）
  const getOrderDetailForQuote = async (belongOrder: number): Promise<any> => {
    return get('/orders_reserve_detail:list', {
      page: 1,
      pageSize: 20,
      filter: `{"$and":[{"f_belong_order":{"$eq":${belongOrder}}}]}`
    })
  }

  return {
    // 基础方法
    request,
    get,
    post,
    put,
    delete: del,

    // 验证码相关
    sendCode,

    // 用户认证
    login,
    authSigin,
    logout,
    getUserInfo,
    refreshToken,

    // 订单管理
    getOrders,
    getOrderDetail,
    createOrder,
    updateOrder,
    deleteOrder,

    // 通用方法
    getCommonData,

    // 报价单生成相关
    createOrders,
    getRecommendCars,
    updateOrders,
    getOrderDetailForQuote,

    // 获取当前 baseURL
    getBaseURL: () => config.public.sellwellautoApiBase
  }
}
