// 赛沃服务
export const useSellwellautoServerApi = () => {
  const config = useRuntimeConfig()
  const { locale } = useI18n()

  // 基础请求方法
  const request = async <T>(
    endpoint: string,
    options: {
      method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
      params?: Record<string, unknown>
      body?: unknown
      headers?: Record<string, string>
    } = {}
  ): Promise<T> => {
    const { method = 'GET', params, body, headers = {} } = options

    // 准备请求头
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-Locale': locale.value === 'zh-CN' ? locale.value : 'en-US',
      ...headers
    }

    // 计算本地时区，形如 +08:00
    const offsetMinutes = -new Date().getTimezoneOffset()
    const sign = offsetMinutes >= 0 ? '+' : '-'
    const abs = Math.abs(offsetMinutes)
    const hours = String(Math.floor(abs / 60)).padStart(2, '0')
    const minutes = String(abs % 60).padStart(2, '0')
    requestHeaders['x-timezone'] = `${sign}${hours}:${minutes}`

    // 添加认证 token（从 Cookie 读取）
    const tokenCookie = useCookie<string>('token', { default: () => '' })
    if (tokenCookie.value) {
      requestHeaders.Authorization = `Bearer ${tokenCookie.value}`
    }

    // 处理查询参数
    let url = endpoint
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      if (searchParams.toString()) {
        url += (url.includes('?') ? '&' : '?') + searchParams.toString()
      }
    }

    try {
      const response: any = await $fetch<T>(url, {
        baseURL: config.public.sellwellautoServerApiBase,
        method,
        body: method !== 'GET' ? (body as BodyInit) : undefined,
        headers: requestHeaders
      })
      return response
    } catch (error: unknown) {
      console.error(`赛沃车联 API请求失败 [${method} ${endpoint}]:`, error)
      const errorObj = error as {
        status?: number
        message?: string
        data?: {
          errors?: Array<{ message?: string; code?: string }>
          message?: string
          error?: string
          code?: string
        }
      }

      const status = errorObj.status
      const data = errorObj.data || {}

      // 统一错误信息
      const normalizedMessage =
        data?.errors?.[0]?.message ||
        data?.message ||
        data?.error ||
        errorObj?.message ||
        '请求失败'

      // 其他错误处理
      if (status === 403) {
        throw new Error('访问被拒绝')
      } else if (status === 404) {
        throw new Error('请求的资源不存在')
      } else if (status && status >= 500) {
        throw new Error('服务器内部错误，请稍后重试')
      }

      // 创建带有标准化消息的错误对象
      const enhancedError = new Error(normalizedMessage)
      ;(enhancedError as any).normalizedMessage = normalizedMessage
      ;(enhancedError as any).status = status

      throw enhancedError
    }
  }

  // 便捷方法
  const get = <T>(
    endpoint: string,
    params?: Record<string, unknown>
  ): Promise<T> => {
    return request<T>(endpoint, { method: 'GET', params })
  }

  const post = <T>(
    endpoint: string,
    body?: unknown,
    params?: Record<string, unknown>
  ): Promise<T> => {
    return request<T>(endpoint, { method: 'POST', body, params })
  }

  const put = <T>(
    endpoint: string,
    body?: unknown,
    params?: Record<string, unknown>
  ): Promise<T> => {
    return request<T>(endpoint, { method: 'PUT', body, params })
  }

  const del = <T>(
    endpoint: string,
    params?: Record<string, unknown>
  ): Promise<T> => {
    return request<T>(endpoint, { method: 'DELETE', params })
  }

  // 解析购车需求
  const parseDemand = (query: string) => {
    return post('/parse-demand', {
      query
    })
  }

  // 获取品牌车系树
  const carTree = () => {
    return get('/car-tree')
  }

  // 汽车搜索任务创建
  const createCarSearchTask = (data: any) => {
    return post('/car-search', data)
  }

  // 查询二手车
  const searchUsedCar = (data: any) => {
    return post('/get-used-car', data)
  }

  return {
    // 基础方法
    request,
    get,
    post,
    put,
    delete: del,

    parseDemand,
    carTree,
    createCarSearchTask,
    searchUsedCar,

    // 获取当前 baseURL
    getBaseURL: () => config.public.sellwellautoApiBase
  }
}
