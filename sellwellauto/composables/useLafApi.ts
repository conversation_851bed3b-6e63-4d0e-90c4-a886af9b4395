// 新闻相关类型 - 与原项目完全一致
export interface NewsItem {
  id: number
  label: string
  image: string
  title: string
  content: string
  createdAt: string
  detail_url: string
  detail_content?: string // 新闻详细内容，支持markdown格式
}

export interface NewsListResponse {
  code: number
  message: string
  data: {
    data: NewsItem[]
    meta: {
      count: number
      page: number
      pageSize: number
      totalPages: number
    }
  }
  meta?: {
    count: number
    page: number
    pageSize: number
    totalPages: number
  }
}

export interface NewsDetailResponse {
  code: number
  message: string
  data: NewsItem
}

export interface NewsQueryParams {
  page: number
  pageSize: number
  category?: string
  keyword?: string
  status?: string
}

// 统一的 LAF API 客户端
export const useLafApi = () => {
  const config = useRuntimeConfig()

  // 基础请求方法
  const request = async <T>(
    endpoint: string,
    options: {
      method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
      params?: Record<string, unknown>
      body?: unknown
      headers?: Record<string, string>
    } = {}
  ): Promise<T> => {
    const { method = 'GET', params, body, headers = {} } = options

    try {
      const response: any = await $fetch<T>(endpoint, {
        baseURL: config.public.lafApiBase,
        method,
        params: method === 'GET' ? params : undefined,
        body: method !== 'GET' ? (body as BodyInit) : undefined,
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
      })
      return response
    } catch (error: unknown) {
      console.error(`LAF API请求失败 [${method} ${endpoint}]:`, error)
      const errorObj = error as { status?: number; message?: string }
      if (errorObj.status === 401) {
        throw new Error('未授权访问，请重新登录')
      } else if (errorObj.status === 403) {
        throw new Error('访问被拒绝')
      } else if (errorObj.status === 404) {
        throw new Error('请求的资源不存在')
      } else if (errorObj.status && errorObj.status >= 500) {
        throw new Error('服务器内部错误，请稍后重试')
      } else {
        throw new Error(errorObj.message || '请求失败，请稍后重试')
      }
    }
  }

  // 便捷方法
  const get = <T>(endpoint: string, params?: Record<string, unknown>): Promise<T> => {
    return request<T>(endpoint, { method: 'GET', params })
  }

  const post = <T>(
    endpoint: string,
    body?: unknown,
    params?: Record<string, unknown>
  ): Promise<T> => {
    return request<T>(endpoint, { method: 'POST', body, params })
  }

  const put = <T>(
    endpoint: string,
    body?: unknown,
    params?: Record<string, unknown>
  ): Promise<T> => {
    return request<T>(endpoint, { method: 'PUT', body, params })
  }

  const del = <T>(endpoint: string, params?: Record<string, unknown>): Promise<T> => {
    return request<T>(endpoint, { method: 'DELETE', params })
  }

  // 注册用户
  const authSignUp = async (data: {
    nickname: string
    username: string
    email: string
    phone: string
    password: string
  }) => {
    return post('/sellwellauto/auth/signUp', data)
  }

  // 新闻相关 API
  const getNewsList = async (params: NewsQueryParams): Promise<NewsListResponse> => {
    return get<NewsListResponse>('/sellwellauto/news/list', {
      page: params.page,
      pageSize: params.pageSize,
      ...(params.category && { category: params.category }),
      ...(params.keyword && { keyword: params.keyword }),
      ...(params.status && { status: params.status }),
    })
  }

  const getNewsDetail = async (id: number): Promise<NewsDetailResponse> => {
    return get<NewsDetailResponse>(`/sellwellauto/news/detail`, { id })
  }

  // 更新用户信息
  const updateUserInfo = async (data: { id: number; remark: string }) => {
    return post('/sellwellauto/users/update', data)
  }

  // 获取汽车品牌
  const getCarBrand = async () => {
    return get('/sellwellauto/car/brand')
  }

  // 获取车系
  const getCarSeries = async (brandId: number) => {
    return get(`/sellwellauto/car/series?brandId=${brandId}`)
  }

  // 获取车型
  const getCarModel = async (seriesId: number) => {
    return get(`/sellwellauto/car/model?seriesId=${seriesId}`)
  }

  return {
    // 基础方法
    request,
    get,
    post,
    put,
    delete: del,

    // 注册用户
    authSignUp,

    // 新闻相关
    getNewsList,
    getNewsDetail,

    // 用户相关
    updateUserInfo,

    // 车辆相关
    getCarBrand,
    getCarSeries,
    getCarModel,

    // 获取当前 baseURL
    getBaseURL: () => config.public.lafApiBase,
  }
}
