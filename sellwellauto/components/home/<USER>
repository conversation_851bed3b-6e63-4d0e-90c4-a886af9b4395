<template>
  <section class="core-functions-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">{{ $t('home.coreFunctions.title') }}</h2>
        <p class="section-subtitle">{{ $t('home.coreFunctions.subtitle') }}</p>
      </div>

      <div class="functions-flow">
        <div v-for="role in userRoles" :key="role.id" class="role-card">
          <div class="role-header">
            <img :src="role.image" :alt="role.title" class="role-icon" />

            <h3 class="role-title">{{ role.title }}</h3>
            <div class="arrow-icon">
              <img src="/images/home/<USER>" :alt="role.title" />
            </div>
          </div>

          <div class="role-content">
            <ul class="role-features">
              <li v-for="feature in role.features" :key="feature">{{ feature }}</li>
            </ul>

            <ActionButton :text="$t('home.coreFunctions.registerButton')" custom-class="join-btn" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ActionButton from '@/components/common/ActionButton.vue'

const { t } = useI18n()

const userRoles = computed(() => [
  {
    id: 'car-source',
    title: t('home.coreFunctions.roles.carSource.title'),
    image: '/images/home/<USER>',
    features: [
      t('home.coreFunctions.roles.carSource.feature1'),
      t('home.coreFunctions.roles.carSource.feature2'),
      t('home.coreFunctions.roles.carSource.feature3'),
    ],
  },
  {
    id: 'foreign-trader',
    title: t('home.coreFunctions.roles.foreignTrader.title'),
    image: '/images/home/<USER>',
    features: [
      t('home.coreFunctions.roles.foreignTrader.feature1'),
      t('home.coreFunctions.roles.foreignTrader.feature2'),
      t('home.coreFunctions.roles.foreignTrader.feature3'),
    ],
  },
  {
    id: 'overseas-buyer',
    title: t('home.coreFunctions.roles.overseasBuyer.title'),
    image: '/images/home/<USER>',
    features: [
      t('home.coreFunctions.roles.overseasBuyer.feature1'),
      t('home.coreFunctions.roles.overseasBuyer.feature2'),
      t('home.coreFunctions.roles.overseasBuyer.feature3'),
    ],
  },
])
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;
@use '@/assets/styles/mixins' as *;

.core-functions-section {
  padding: 30px 0 80px 0;
}

.container {
  max-width: $max-width;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #141414;
  margin-bottom: 20px;
}

.section-subtitle {
  font-size: 1.6rem;
  color: #919191;
}

.functions-flow {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 40px;
  position: relative;
}

.role-card {
  background: white;
  border-radius: 16px;
  padding: 30px 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  flex: 1;
  max-width: 320px;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }
}

.role-header {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  gap: 12px;
  border-bottom: 1px solid #e5e5e5;
  padding: 0 25px 25px 25px;
}

.role-icon {
  width: 30px;
  height: 30px;
  background: $primary-color;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  border-radius: 4px;
}

.role-title {
  font-size: 1.8rem;
  font-weight: bold;
  color: $primary-color;
  margin: 0;
  flex: 1;
}

.arrow-icon {
  color: $primary-color;
  flex-shrink: 0;
}

.role-content {
  padding: 0 25px;
}

.role-features {
  list-style: disc;
  margin: 0 0 25px 0;
  padding-left: 20px;
}

.role-features li {
  padding: 6px 0;
  color: #666;
  font-size: 0.95rem;
  line-height: 1.4;
}

.join-btn {
  width: 100%;
  height: 44px;
  background: $primary-color;
  border: none;
  font-size: 15px;
  font-weight: bold;
  border-radius: 8px;

  &:hover {
    background: #e55a2b;
  }
}

.flow-arrow {
  color: $primary-color;
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

@media (max-width: 768px) {
  .functions-flow {
    flex-direction: column;
    gap: 30px;
  }

  .role-card {
    min-width: 280px;
    max-width: 100%;
  }

  .flow-arrow {
    display: none;
  }

  .section-title {
    font-size: 2rem;
  }
}
</style>
