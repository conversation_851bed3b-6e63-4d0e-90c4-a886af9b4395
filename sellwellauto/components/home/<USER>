<template>
  <section class="user-growth-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">{{ $t('home.userGrowth.title') }}</h2>
      </div>

      <div class="testimonials-container">
        <div v-for="(testimonial, index) in testimonials" :key="index" class="testimonial-section">
          <div class="testimonial-card">
            <div class="testimonial-content" :class="{ reverse: index % 2 === 1 }">
              <div class="avatar-container">
                <div class="avatar">
                  <img :src="testimonial.avatar" :alt="testimonial.name" />
                </div>
              </div>

              <div class="text-content">
                <p class="testimonial-quote">{{ testimonial.content }}</p>
                <div class="customer-background">{{ testimonial.background }}</div>
                <div class="kpi-container">
                  <div v-for="(kpi, kpiIndex) in testimonial.kpis" :key="kpiIndex" class="kpi-item">
                    <div class="kpi-number">{{ kpi.number }}</div>
                    <div class="kpi-label">{{ kpi.label }}</div>
                  </div>
                </div>
                <div class="testimonial-highlight">{{ testimonial.highlight }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const { t } = useI18n()

const testimonials = computed(() => [
  {
    content: t('home.userGrowth.testimonials.user1.content'),
    background: t('home.userGrowth.testimonials.user1.background'),
    kpis: [
      {
        number: t('home.userGrowth.testimonials.user1.kpis.exportedCars'),
        label: t('home.userGrowth.testimonials.user1.kpiLabels.exportedCars'),
      },
      {
        number: t('home.userGrowth.testimonials.user1.kpis.exportAmount'),
        label: t('home.userGrowth.testimonials.user1.kpiLabels.exportAmount'),
      },
      {
        number: t('home.userGrowth.testimonials.user1.kpis.firstOrderTime'),
        label: t('home.userGrowth.testimonials.user1.kpiLabels.firstOrderTime'),
      },
    ],
    highlight: t('home.userGrowth.testimonials.user1.highlight'),
    avatar: '/images/home/<USER>',
    name: t('home.userGrowth.testimonials.user1.name'),
  },
  {
    content: t('home.userGrowth.testimonials.user2.content'),
    background: t('home.userGrowth.testimonials.user2.background'),
    kpis: [
      {
        number: t('home.userGrowth.testimonials.user2.kpis.exportAmount'),
        label: t('home.userGrowth.testimonials.user2.kpiLabels.exportAmount'),
      },
      {
        number: t('home.userGrowth.testimonials.user2.kpis.ranking'),
        label: t('home.userGrowth.testimonials.user2.kpiLabels.ranking'),
      },
      {
        number: t('home.userGrowth.testimonials.user2.kpis.efficiency'),
        label: t('home.userGrowth.testimonials.user2.kpiLabels.efficiency'),
      },
    ],
    highlight: t('home.userGrowth.testimonials.user2.highlight'),
    avatar: '/images/home/<USER>',
    name: t('home.userGrowth.testimonials.user2.name'),
  },
])
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;
@use '@/assets/styles/mixins' as *;

.user-growth-section {
  background: url('/images/home/<USER>') no-repeat center center;
  background-size: cover;
  color: white;
  padding: 80px 0;
  position: relative;
}

.container {
  max-width: $max-width;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 20px;
}

.testimonials-container {
  max-width: $max-width;
  margin: 0 auto;
}

.testimonial-section {
  margin-bottom: 40px;
}

.testimonial-card {
  background: #f1e5ff;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.testimonial-content {
  display: flex;
  align-items: center;
  gap: 30px;
  padding: 40px 180px 30px 60px;

  &.reverse {
    flex-direction: row-reverse;
    padding: 40px 60px 30px 180px;
  }
}

.avatar-container {
  flex-shrink: 0;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.text-content {
  flex: 1;
}

.testimonial-quote {
  font-size: 1.5rem;
  line-height: 1.6;
  margin-bottom: 4px;
  color: $primary-color;
  font-weight: 500;
}

.customer-background {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 25px;
  color: #666;
  text-align: left;
}

.kpi-container {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  justify-content: space-between;
}

.kpi-item {
  text-align: left;
  flex: 1;
}

.kpi-number {
  font-size: 2.4rem;
  font-weight: bold;
  color: #ea5321;
}

.kpi-label {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.3;
}

.testimonial-highlight {
  font-size: 1.1rem;
  font-weight: 500;
  color: #666;
  background: none;
  padding: 0;
  border-radius: 0;
  display: block;
  box-shadow: none;
  text-align: center;
  margin-top: 30px;
}

@media (max-width: 1200px) {
  .testimonial-content {
    padding: 40px 100px 30px 60px;
  }

  .testimonial-content.reverse {
    padding: 40px 60px 30px 100px;
  }

  .kpi-container {
    gap: 20px;
  }

  .kpi-number {
    font-size: 2rem;
  }
}

@media (max-width: 992px) {
  .testimonial-content {
    padding: 40px 60px 30px 40px;
  }

  .testimonial-content.reverse {
    padding: 40px 40px 30px 60px;
  }

  .kpi-container {
    gap: 15px;
  }

  .kpi-number {
    font-size: 1.8rem;
  }

  .kpi-label {
    font-size: 0.75rem;
  }
}

@media (max-width: 768px) {
  .user-growth-section {
    padding: 60px 0;
  }

  .section-header {
    margin-bottom: 40px;
  }

  .section-title {
    font-size: 2rem;
  }

  .testimonial-content {
    flex-direction: column;
    gap: 20px;
    padding: 30px 20px;
    text-align: center;
  }

  .testimonial-content.reverse {
    flex-direction: column;
    padding: 30px 20px;
  }

  .text-content {
    text-align: center;
  }

  .customer-background {
    text-align: center;
    font-size: 0.9rem;
    margin-bottom: 20px;
  }

  .kpi-container {
    flex-direction: column;
    gap: 15px;
    margin-bottom: 15px;
  }

  .kpi-item {
    text-align: center;
  }

  .testimonial-card {
    padding: 0;
  }

  .testimonial-quote {
    font-size: 1.2rem;
    margin-bottom: 8px;
  }

  .testimonial-highlight {
    font-size: 1rem;
    margin-top: 20px;
  }

  .avatar {
    width: 60px;
    height: 60px;
  }

  .kpi-number {
    font-size: 1.6rem;
  }

  .kpi-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .user-growth-section {
    padding: 40px 0;
  }

  .section-title {
    font-size: 1.8rem;
    margin-bottom: 15px;
  }

  .testimonial-content {
    padding: 25px 15px;
  }

  .testimonial-quote {
    font-size: 1.1rem;
    line-height: 1.5;
  }

  .customer-background {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .kpi-number {
    font-size: 1.4rem;
  }

  .kpi-label {
    font-size: 0.75rem;
  }

  .testimonial-highlight {
    font-size: 0.9rem;
    margin-top: 15px;
  }

  .avatar {
    width: 50px;
    height: 50px;
  }
}
</style>
