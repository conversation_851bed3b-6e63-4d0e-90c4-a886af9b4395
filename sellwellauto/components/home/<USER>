<template>
  <div class="generate-quote-container">
    <div class="quote-card">
      <!-- 输入字段区域 -->
      <div class="input-section">
        <el-form :model="searchForm" class="quote-form">
          <!-- 手动选车模式 -->
          <div
            v-if="inquiryMethod === $t('generateQuote.manualMode.title')"
            class="form-row"
          >
            <el-form-item class="form-item">
              <el-select
                v-model="searchForm.f_brand"
                :placeholder="$t('generateQuote.manualMode.brand')"
                class="form-select"
                clearable
                filterable
                :loading="brandLoading"
                :no-data-text="$t('generateQuote.manualMode.noData')"
                @change="handleBrandChange"
              >
                <el-option
                  v-for="f_brand in brandOptions"
                  :key="f_brand.id"
                  :label="f_brand.name"
                  :value="f_brand.id"
                />
                <template #loading>
                  <el-icon class="is-loading">
                    <svg class="circular" viewBox="0 0 20 20">
                      <g
                        class="path2 loading-path"
                        stroke-width="0"
                        style="animation: none; stroke: none"
                      >
                        <circle r="3.375" class="dot1" rx="0" ry="0" />
                        <circle r="3.375" class="dot2" rx="0" ry="0" />
                        <circle r="3.375" class="dot4" rx="0" ry="0" />
                        <circle r="3.375" class="dot3" rx="0" ry="0" />
                      </g>
                    </svg>
                  </el-icon>
                </template>
              </el-select>
            </el-form-item>

            <el-form-item class="form-item">
              <el-select
                v-model="searchForm.f_series"
                :placeholder="$t('generateQuote.manualMode.series')"
                class="form-select"
                clearable
                filterable
                :disabled="!searchForm.f_brand"
                :loading="seriesLoading"
                :no-data-text="$t('generateQuote.manualMode.noData')"
              >
                <el-option
                  v-for="f_series in seriesOptions"
                  :key="f_series.id"
                  :label="f_series.name"
                  :value="f_series.id"
                />
                <template #loading>
                  <el-icon class="is-loading">
                    <svg class="circular" viewBox="0 0 20 20">
                      <g
                        class="path2 loading-path"
                        stroke-width="0"
                        style="animation: none; stroke: none"
                      >
                        <circle r="3.375" class="dot1" rx="0" ry="0" />
                        <circle r="3.375" class="dot2" rx="0" ry="0" />
                        <circle r="3.375" class="dot4" rx="0" ry="0" />
                        <circle r="3.375" class="dot3" rx="0" ry="0" />
                      </g>
                    </svg>
                  </el-icon>
                </template>
              </el-select>
            </el-form-item>

            <el-button
              type="primary"
              class="generate-btn"
              :loading="loading"
              :icon="Search"
              @click="searchUsedCar"
              >{{ $t('common.search') }}</el-button
            >
          </div>

          <!-- 智能找车模式 -->
          <div v-else class="form-row">
            <el-form-item class="form-item">
              <el-input
                ref="smartSearchInput"
                v-model="searchForm.smart_search"
                type="textarea"
                :rows="3"
                :placeholder="$t('generateQuote.smartMode.placeholder')"
                class="form-input"
                maxlength="200"
              />
            </el-form-item>

            <el-button
              type="primary"
              class="generate-btn"
              :loading="loading"
              :icon="Search"
              @click="searchUsedCar"
              >{{ $t('common.search') }}</el-button
            >
          </div>
        </el-form>
      </div>

      <!-- 热力榜区域 -->
      <HotRankMarquee />

      <div class="orders-type-switch">
        <div class="switch-container">
          <div
            class="switch-slider"
            :class="{
              'slide-right':
                inquiryMethod === $t('generateQuote.smartMode.title')
            }"
          />
          <div
            class="switch-option"
            :class="{
              active: inquiryMethod === $t('generateQuote.manualMode.title')
            }"
            @click="inquiryMethod = $t('generateQuote.manualMode.title')"
          >
            <el-icon class="switch-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"
                />
              </svg>
            </el-icon>
            <span class="switch-text">{{
              $t('generateQuote.manualMode.title')
            }}</span>
          </div>
          <div
            class="switch-option"
            :class="{
              active: inquiryMethod === $t('generateQuote.smartMode.title')
            }"
            @click="inquiryMethod = $t('generateQuote.smartMode.title')"
          >
            <el-icon class="switch-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M13 2.05v3.03c3.39.49 6 3.4 6 6.92 0 .9-.18 1.75-.5 2.54l2.6 1.53c.56-1.24.9-2.62.9-4.07 0-5.18-3.95-9.45-9-9.95zM12 19c-3.87 0-7-3.13-7-7 0-3.53 2.61-6.43 6-6.92V2.05c-5.06.5-9 4.76-9 9.95 0 5.52 4.47 10 9.99 10 3.31 0 6.24-1.61 8.06-4.09l-2.6-1.53C16.17 17.98 14.21 19 12 19z"
                />
              </svg>
            </el-icon>
            <span class="switch-text">{{
              $t('generateQuote.smartMode.title')
            }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'

const { locale } = useI18n()
const { carTree } = useSellwellautoServerApi()
const searchStore = useSearchStore()

// 在服务端渲染时获取品牌数据
const {
  data: carTreeData,
  pending: brandLoading,
  refresh: refreshCarTree
} = await useLazyAsyncData(
  'carTree',
  async () => {
    try {
      return await carTree()
    } catch (err) {
      console.error('获取品牌车系数据失败:', err)
      return null
    }
  },
  {
    server: true, // 确保在服务端执行
    default: () => null
  }
)

// 监听语言变化，重新获取品牌数据
watch(
  () => locale.value,
  async (newLocale, oldLocale) => {
    if (newLocale !== oldLocale) {
      await refreshCarTree()
    }
  },
  { immediate: false }
)

// 表单数据
const searchForm = reactive({
  f_brand: null,
  f_series: null,
  smart_search: '' // 新增智能找车模式输入框
})

// 加载状态
const loading = ref(false)
const inquiryMethod = ref(
  locale.value === 'zh-CN' ? '手动选车' : 'Manual Car Selection'
)
const seriesLoading = ref(false)

// 智能搜索输入框的ref
const smartSearchInput = ref()

// 监听询价方式变化，自动聚焦输入框
watch(inquiryMethod, async (newValue) => {
  if (newValue === $t('generateQuote.smartMode.title')) {
    // 等待DOM更新完成后聚焦输入框
    await nextTick()
    if (smartSearchInput.value) {
      const inputElement =
        smartSearchInput.value.$el?.querySelector('input') ||
        smartSearchInput.value.$el
      if (inputElement) {
        inputElement.focus()
      }
    }
  }
})

watch(locale, () => {
  inquiryMethod.value =
    locale.value === 'zh-CN' ? '手动选车' : 'Manual Car Selection'
})

// 监听语言变化，重新获取品牌数据
watch(
  () => locale.value,
  async (newLocale, oldLocale) => {
    if (newLocale !== oldLocale) {
      await refreshCarTree()
    }
  },
  { immediate: false }
)

// 处理品牌数据，提取品牌选项
const brandOptions = computed(() => {
  if (!carTreeData.value) return []

  // 根据实际返回的数据结构处理品牌数据
  try {
    const data = carTreeData.value as any
    const tree = data?.data?.tree || []

    return tree.map((brand: any) => ({
      id: brand.id,
      name: brand.name
    }))
  } catch (error) {
    console.error('处理品牌数据时出错:', error)
    return []
  }
})

// 处理车系数据
const seriesOptions = computed(() => {
  if (!carTreeData.value || !searchForm.f_brand) return []

  try {
    const data = carTreeData.value as any
    const tree = data?.data?.tree || []
    const selectedBrand = tree.find(
      (brand: any) => brand.id === searchForm.f_brand
    )

    if (!selectedBrand || !selectedBrand.children) return []

    return selectedBrand.children.map((series: any) => ({
      id: series.id,
      name: series.name
    }))
  } catch (error) {
    console.error('处理车系数据时出错:', error)
    return []
  }
})

// 监听品牌选择变化，清空车系选择
watch(
  () => searchForm.f_brand,
  () => {
    searchForm.f_series = null
  }
)

// 处理品牌变化
const handleBrandChange = () => {
  searchForm.f_series = null
}

// 跳转到搜索页面并传递搜索条件
const searchUsedCar = async () => {
  try {
    loading.value = true

    if (inquiryMethod.value === $t('generateQuote.smartMode.title')) {
      // 智能搜索模式
      if (!searchForm.smart_search.trim()) {
        ElMessage.warning('请输入搜索内容')
        return
      }

      // 直接设置搜索文本到状态管理
      searchStore.setSearchCondition({
        search_text: searchForm.smart_search,
        brand_id: null,
        series_id: null
      })

      // 跳转到搜索页面
      const router = useRouter()
      await router.push('/usedcar/search')
    } else {
      // 手动选车模式
      if (!searchForm.f_brand) {
        ElMessage.warning('请选择品牌')
        return
      }

      // 设置搜索条件到状态管理
      searchStore.setSearchCondition({
        search_text: '',
        brand_id: searchForm.f_brand,
        series_id: searchForm.f_series
      })

      // 跳转到搜索页面
      const router = useRouter()
      await router.push('/usedcar/search')
    }
  } catch (error: any) {
    console.error('跳转失败:', error)
    ElMessage.error(error.message || '跳转失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.generate-quote-container {
  position: relative;
  transform: translateY(-80px);
  min-height: 200px;
  z-index: 20;
}

.quote-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 40px 30px 30px 30px;
  max-width: $max-width;
  margin: 0 auto;
  position: relative;
}

.orders-type-switch {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);

  .switch-container {
    display: flex;
    background: #f5f5f5;
    border-radius: 25px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    position: relative;
    overflow: hidden;

    .switch-slider {
      position: absolute;
      top: 2.5px;
      left: 2px;
      width: calc(50% - 4px);
      height: calc(100% - 6px);
      background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
      border-radius: 40px;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 1;

      &.slide-right {
        transform: translateX(calc(100% + 4px));
      }
    }

    .switch-option {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      border-radius: 21px;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 100px;
      justify-content: center;
      position: relative;
      overflow: hidden;
      z-index: 2;
      flex: 1;

      &.active {
        color: white;
        font-weight: 600;

        .switch-icon {
          color: white;
        }

        .switch-text {
          color: white;
          font-weight: 600;
        }
      }

      &:not(.active) {
        color: #666;

        .switch-icon {
          color: #999;
        }

        .switch-text {
          color: #666;
        }
      }

      .switch-icon {
        font-size: 18px;
        transition: color 0.3s ease;
      }

      .switch-text {
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        white-space: nowrap;
      }
    }
  }
}

.input-section {
  margin-bottom: 30px;
}

.quote-form {
  .form-row {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
  }

  .form-item {
    margin-bottom: 0;
    flex: 1;
    min-width: 150px;
  }

  .form-select {
    width: 100%;
  }

  .form-input {
    width: 100%;
  }

  .generate-btn {
    background: linear-gradient(135deg, $primary-color 0%, #f7931e 100%);
    border: none;
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    min-width: 140px;
    height: 40px;
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;

    &:hover {
      background: linear-gradient(135deg, #e55a2b 0%, #e0851a 100%);
      box-shadow: 0 10px 20px rgba(255, 107, 53, 0.4);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s ease;
    }

    &:hover::before {
      left: 100%;
    }
  }
}

// loading 弹窗样式
.loading-modal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 0 8px;
}

.loading-modal__icon {
  color: var(--el-color-primary);
  margin-bottom: 10px;
}

.loading-modal__text {
  font-size: 14px;
  color: #606266;
}

.success-text {
  margin-top: 10px;
}

.success-text-link {
  color: $primary-color;
  cursor: pointer;
}

// 响应式设计
@media (max-width: 768px) {
  .generate-quote-container {
    padding: 10px;
  }

  .quote-card {
    padding: 20px;
  }

  .quote-form .form-row {
    gap: 10px;
  }

  .form-item {
    min-width: auto;
  }

  .generate-btn {
    width: 100%;
  }

  .hot-rank-list {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .separator {
    display: none;
  }
}

.el-select-dropdown__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  font-size: 20px;
}

.circular {
  display: inline;
  height: 30px;
  width: 20px;
  animation: loading-rotate 2s linear infinite;
  border-radius: 4px;
}

.path {
  animation: loading-dash 1.5s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: var(--el-color-primary);
  stroke-linecap: round;
}

.loading-path .dot1 {
  transform: translate(3.75px, 3.75px);
  fill: var(--el-color-primary);
  animation: custom-spin-move 1s infinite linear alternate;
  opacity: 0.3;
}

.loading-path .dot2 {
  transform: translate(calc(100% - 3.75px), 3.75px);
  fill: var(--el-color-primary);
  animation: custom-spin-move 1s infinite linear alternate;
  opacity: 0.3;
  animation-delay: 0.4s;
}

.loading-path .dot3 {
  transform: translate(3.75px, calc(100% - 3.75px));
  fill: var(--el-color-primary);
  animation: custom-spin-move 1s infinite linear alternate;
  opacity: 0.3;
  animation-delay: 1.2s;
}

.loading-path .dot4 {
  transform: translate(calc(100% - 3.75px), calc(100% - 3.75px));
  fill: var(--el-color-primary);
  animation: custom-spin-move 1s infinite linear alternate;
  opacity: 0.3;
  animation-delay: 0.8s;
}

@keyframes loading-rotate {
  to {
    transform: rotate(360deg);
  }
}

@keyframes loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40px;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120px;
  }
}

@keyframes custom-spin-move {
  to {
    opacity: 1;
  }
}
</style>
