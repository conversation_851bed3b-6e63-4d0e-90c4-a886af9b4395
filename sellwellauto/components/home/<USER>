<template>
  <section class="contact-section">
    <div class="container">
      <div class="contact-content">
        <div class="map-container">
          <a
            href="https://map.baidu.com/search/%E6%B7%B1%E5%9C%B3%E5%B8%82%E8%B5%9B%E6%B2%83%E8%BD%A6%E8%81%94%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/@12694733.955,2570059.6,19z?querytype=s&da_src=shareurl&wd=%E6%B7%B1%E5%9C%B3%E5%B8%82%E8%B5%9B%E6%B2%83%E8%BD%A6%E8%81%94%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8&c=340&src=0&pn=0&sug=0&l=13&b=(12664952.*********,2554421.7100000577;12730584.*********,2591701.7100000577)&from=webmap&biz_forward=%7B%22scaler%22:2,%22styles%22:%22pl%22%7D&device_ratio=2"
            target="_blank"
            rel="noreferrer noopener"
          >
            <img src="/images/home/<USER>" :alt="$t('contactSection.mapAlt')" />
          </a>
        </div>

        <div class="contact-info">
          <h2 class="contact-title">{{ $t('contactSection.title') }}</h2>

          <div class="company-info">
            <div class="info-item">
              <el-icon size="20"><OfficeBuilding /></el-icon>
              <span>{{ $t('contactSection.companyInfo.companyName') }}</span>
            </div>
            <div class="info-item">
              <el-icon size="20"><Location /></el-icon>
              <span>{{ $t('contactSection.companyInfo.address') }}</span>
            </div>
          </div>

          <div class="qr-codes">
            <div class="qr-item">
              <img
                src="/images/home/<USER>"
                :alt="$t('contactSection.qrCodes.wechatOfficial')"
              />
              <span class="qr-label">{{ $t('contactSection.qrCodes.wechatOfficialLabel') }}</span>
            </div>
            <div class="qr-item">
              <img src="/images/home/<USER>" :alt="$t('contactSection.qrCodes.wechatWork')" />
              <span class="qr-label">{{ $t('contactSection.qrCodes.wechatWorkLabel') }}</span>
            </div>
            <div class="qr-item">
              <img src="/images/home/<USER>" :alt="$t('contactSection.qrCodes.douyin')" />
              <span class="qr-label">{{ $t('contactSection.qrCodes.douyinLabel') }}</span>
            </div>
          </div>

          <div class="copyright">
            {{ $t('contactSection.copyright') }}
            <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener">{{
              $t('contactSection.icp')
            }}</a>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// Element Plus图标导入
import { Location, OfficeBuilding } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.contact-section {
  background: #ebeced;
  max-width: 100vw;
}

.container {
  max-width: $max-width;
  margin: 0 auto;
  padding: 50px 0;
}

.contact-content {
  @include flex-between;
}

.map-container {
  width: 640px;
  height: 440px;
  overflow: hidden;
  border-radius: 10px;

  img {
    width: 640px;
    height: 440px;
    object-fit: cover;
  }
}

.map-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;

  p {
    margin: 10px 0 0 0;
    font-size: 1.1rem;
  }

  .map-note {
    font-size: 0.9rem;
    color: #999;
    margin-top: 5px;
  }
}

.contact-info {
  padding: 40px 50px;
  display: flex;
  flex-direction: column;
  gap: 30px;
  flex: 1;
}

.contact-title {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin: 0 0 20px 0;
}

.company-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 0;

  span {
    font-size: 0.95rem;
    line-height: 1.4;
  }
}

.qr-codes {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.qr-item {
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  img {
    width: 80px;
    height: 80px;
    object-fit: fill;
  }
}

.qr-label {
  font-size: 0.8rem;
  color: #666;
  text-align: center;
}

.copyright {
  font-size: 1rem;
  color: #666;
  margin-top: 40px;

  a {
    color: $primary-color;
    text-decoration: none;
  }
}

@media (max-width: 768px) {
  .contact-content {
    flex-direction: column;
    gap: 20px;
  }

  .contact-info {
    padding: 30px 20px;
  }

  .contact-title {
    font-size: 1.5rem;
  }

  .map-container {
    width: 100%;
    min-height: 300px;

    img {
      width: 100%;
      height: 300px;
    }
  }

  .qr-codes {
    gap: 15px;
  }

  .qr-item {
    width: 60px;
    height: 60px;

    img {
      width: 60px;
      height: 60px;
    }
  }
}
</style>
