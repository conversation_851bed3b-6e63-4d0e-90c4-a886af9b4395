<template>
  <div class="generate-quote-container">
    <div class="quote-card">
      <!-- 输入字段区域 -->
      <div class="input-section">
        <el-form :model="searchForm" class="quote-form">
          <!-- 手动选车模式 -->
          <div
            v-if="inquiryMethod === $t('generateQuote.manualMode.title')"
            class="form-row"
          >
            <el-form-item class="form-item">
              <el-select
                v-model="searchForm.f_brand"
                :placeholder="$t('generateQuote.manualMode.brand')"
                class="form-select"
                clearable
                filterable
                :loading="brandLoading"
                :no-data-text="$t('generateQuote.manualMode.noData')"
                @change="handleBrandChange"
              >
                <el-option
                  v-for="f_brand in brandOptions"
                  :key="f_brand.id"
                  :label="f_brand.name"
                  :value="f_brand.id"
                />
                <template #loading>
                  <el-icon class="is-loading">
                    <svg class="circular" viewBox="0 0 20 20">
                      <g
                        class="path2 loading-path"
                        stroke-width="0"
                        style="animation: none; stroke: none"
                      >
                        <circle r="3.375" class="dot1" rx="0" ry="0" />
                        <circle r="3.375" class="dot2" rx="0" ry="0" />
                        <circle r="3.375" class="dot4" rx="0" ry="0" />
                        <circle r="3.375" class="dot3" rx="0" ry="0" />
                      </g>
                    </svg>
                  </el-icon>
                </template>
              </el-select>
            </el-form-item>

            <el-form-item class="form-item">
              <el-select
                v-model="searchForm.f_series"
                :placeholder="$t('generateQuote.manualMode.series')"
                class="form-select"
                clearable
                filterable
                :disabled="!searchForm.f_brand"
                :loading="seriesLoading"
                :no-data-text="$t('generateQuote.manualMode.noData')"
                @change="handleSeriesChange"
              >
                <el-option
                  v-for="f_series in seriesOptions"
                  :key="f_series.id"
                  :label="f_series.name"
                  :value="f_series.id"
                />
                <template #loading>
                  <el-icon class="is-loading">
                    <svg class="circular" viewBox="0 0 20 20">
                      <g
                        class="path2 loading-path"
                        stroke-width="0"
                        style="animation: none; stroke: none"
                      >
                        <circle r="3.375" class="dot1" rx="0" ry="0" />
                        <circle r="3.375" class="dot2" rx="0" ry="0" />
                        <circle r="3.375" class="dot4" rx="0" ry="0" />
                        <circle r="3.375" class="dot3" rx="0" ry="0" />
                      </g>
                    </svg>
                  </el-icon>
                </template>
              </el-select>
            </el-form-item>

            <el-form-item class="form-item">
              <el-select
                v-model="searchForm.f_car_model"
                :placeholder="$t('generateQuote.manualMode.model')"
                class="form-select"
                clearable
                filterable
                :disabled="!searchForm.f_series"
                :loading="modelLoading"
                :no-data-text="$t('generateQuote.manualMode.noData')"
              >
                <el-option
                  v-for="f_car_model in modelOptions"
                  :key="f_car_model.id"
                  :label="f_car_model.name"
                  :value="f_car_model.id"
                />
                <template #loading>
                  <el-icon class="is-loading">
                    <svg class="circular" viewBox="0 0 20 20">
                      <g
                        class="path2 loading-path"
                        stroke-width="0"
                        style="animation: none; stroke: none"
                      >
                        <circle r="3.375" class="dot1" rx="0" ry="0" />
                        <circle r="3.375" class="dot2" rx="0" ry="0" />
                        <circle r="3.375" class="dot4" rx="0" ry="0" />
                        <circle r="3.375" class="dot3" rx="0" ry="0" />
                      </g>
                    </svg>
                  </el-icon>
                </template>
              </el-select>
            </el-form-item>

            <el-form-item class="form-item">
              <el-select
                v-model="searchForm.car_type"
                :placeholder="$t('generateQuote.manualMode.carType')"
                class="form-select"
                clearable
                filterable
                disabled
              >
                <el-option
                  v-for="type in typeOptions"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
                <template #loading>
                  <el-icon class="is-loading">
                    <svg class="circular" viewBox="0 0 20 20">
                      <g
                        class="path2 loading-path"
                        stroke-width="0"
                        style="animation: none; stroke: none"
                      >
                        <circle r="3.375" class="dot1" rx="0" ry="0" />
                        <circle r="3.375" class="dot2" rx="0" ry="0" />
                        <circle r="3.375" class="dot4" rx="0" ry="0" />
                        <circle r="3.375" class="dot3" rx="0" ry="0" />
                      </g>
                    </svg>
                  </el-icon>
                </template>
              </el-select>
            </el-form-item>

            <el-button
              type="primary"
              class="generate-btn"
              :loading="loading"
              @click="handleManualQuote"
              >{{ $t('generateQuote.manualMode.generateButton') }}</el-button
            >
          </div>

          <!-- 智能找车模式 -->
          <div v-else class="form-row">
            <el-form-item class="form-item">
              <el-input
                ref="smartSearchInput"
                v-model="searchForm.smart_search"
                type="textarea"
                :rows="3"
                :placeholder="$t('generateQuote.smartMode.placeholder')"
                class="form-input"
                maxlength="200"
              />
            </el-form-item>

            <el-button
              type="primary"
              class="generate-btn"
              :loading="loading"
              @click="handleSmartQuote"
              >{{ $t('generateQuote.smartMode.generateButton') }}</el-button
            >
          </div>
        </el-form>
      </div>

      <!-- 热力榜区域 -->
      <HotRankMarquee />

      <div class="orders-type-switch">
        <div class="switch-container">
          <div
            class="switch-slider"
            :class="{
              'slide-right':
                inquiryMethod === $t('generateQuote.smartMode.title')
            }"
          />
          <div
            class="switch-option"
            :class="{
              active: inquiryMethod === $t('generateQuote.manualMode.title')
            }"
            @click="inquiryMethod = $t('generateQuote.manualMode.title')"
          >
            <el-icon class="switch-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"
                />
              </svg>
            </el-icon>
            <span class="switch-text">{{
              $t('generateQuote.manualMode.title')
            }}</span>
          </div>
          <div
            class="switch-option"
            :class="{
              active: inquiryMethod === $t('generateQuote.smartMode.title')
            }"
            @click="inquiryMethod = $t('generateQuote.smartMode.title')"
          >
            <el-icon class="switch-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M13 2.05v3.03c3.39.49 6 3.4 6 6.92 0 .9-.18 1.75-.5 2.54l2.6 1.53c.56-1.24.9-2.62.9-4.07 0-5.18-3.95-9.45-9-9.95zM12 19c-3.87 0-7-3.13-7-7 0-3.53 2.61-6.43 6-6.92V2.05c-5.06.5-9 4.76-9 9.95 0 5.52 4.47 10 9.99 10 3.31 0 6.24-1.61 8.06-4.09l-2.6-1.53C16.17 17.98 14.21 19 12 19z"
                />
              </svg>
            </el-icon>
            <span class="switch-text">{{
              $t('generateQuote.smartMode.title')
            }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 不可关闭的加载弹窗 -->
  <el-dialog
    v-model="loading"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="360px"
    align-center
  >
    <div class="loading-modal">
      <div v-if="!generateSuccess" class="loading-modal">
        <el-icon class="loading-modal__icon is-loading" :size="36">
          <Loading />
        </el-icon>
        <div class="loading-modal__text">
          {{ $t('generateQuote.loading.title') }}
        </div>
      </div>

      <div v-else class="loading-modal">
        <el-icon class="success-icon" :size="36" color="#67C23A">
          <CircleCheckFilled />
        </el-icon>
        <div class="success-text">
          {{ $t('generateQuote.loading.success') }}
          <span class="success-text-link" @click="handleGoToAdmin">{{
            $t('generateQuote.loading.viewLink')
          }}</span>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { Loading, CircleCheckFilled } from '@element-plus/icons-vue'
import { useLafApi } from '~/composables/useLafApi'
import { useSellwellautoApi } from '~/composables/useSellwellautoApi'
import { useUserStore } from '~/stores/user'

const userStore = useUserStore()
const { getCarBrand, getCarSeries, getCarModel } = useLafApi()
const { createOrders, getRecommendCars, getOrderDetailForQuote, updateOrders } =
  useSellwellautoApi()
const { locale } = useI18n()

// 定义数据类型接口
interface _CarBrand {
  id: number
  name: string
}

interface CarSeries {
  id: number
  name: string
}

interface CarModel {
  id: number
  name: string
}

// 组件挂载时处理数据和表单恢复
onMounted(async () => {
  // 设置初始询价方式
  inquiryMethod.value = $t('generateQuote.manualMode.title')

  // 设置初始车辆类型
  searchForm.car_type = $t('generateQuote.manualMode.carType')

  // 如果服务端获取失败，客户端重试
  if (
    import.meta.client &&
    (!brandsResponse.value || !(brandsResponse.value as any)?.data?.length)
  ) {
    // 服务端没有数据，客户端重新获取
    await refresh()
  }

  await restoreFormData()
})

// 表单数据
const searchForm = reactive({
  f_brand: '',
  f_series: '',
  f_car_model: '',
  car_type: '',
  smart_search: '' // 新增智能找车模式输入框
})

// 加载状态
const loading = ref(false)
const ordersId = ref(null)
const inquiryMethod = ref(
  locale.value === 'zh-CN' ? '手动选车' : 'Manual Car Selection'
)
const generateSuccess = ref(false)
const seriesLoading = ref(false)
const modelLoading = ref(false)
const isUnmounted = ref(false) // 卸载标记（用于中止轮询）

// 智能搜索输入框的ref
const smartSearchInput = ref()

// 监听询价方式变化，自动聚焦输入框
watch(inquiryMethod, async (newValue) => {
  if (newValue === $t('generateQuote.smartMode.title')) {
    // 等待DOM更新完成后聚焦输入框
    await nextTick()
    if (smartSearchInput.value) {
      const inputElement =
        smartSearchInput.value.$el?.querySelector('input') ||
        smartSearchInput.value.$el
      if (inputElement) {
        inputElement.focus()
      }
    }
  }
})

watch(locale, () => {
  inquiryMethod.value =
    locale.value === 'zh-CN' ? '手动选车' : 'Manual Car Selection'
})

// 在服务端预获取数据，避免水合不一致问题
const {
  data: brandsResponse,
  pending: brandLoading,
  refresh
} = await useAsyncData('car-brands', () => getCarBrand(), {
  server: true, // 确保服务端执行
  default: () => ({ data: [] }) // 提供默认值避免空状态
})

// 从异步数据中提取品牌选项
const brandOptions = computed(() => {
  return (brandsResponse.value as any)?.data || []
})

// 车系选项
const seriesOptions = ref<CarSeries[]>([])

// 车型选项
const modelOptions = ref<CarModel[]>([])

// 二手车类型选项
const typeOptions = computed(() => [
  {
    label: $t('generateQuote.manualMode.carType'),
    value: $t('generateQuote.manualMode.carType')
  }
])

// 本地存储键名
const STORAGE_KEY = 'quote_form_data'

// 保存表单数据到本地存储
const saveFormData = () => {
  const formData = {
    f_brand: searchForm.f_brand,
    f_series: searchForm.f_series,
    f_car_model: searchForm.f_car_model,
    car_type: searchForm.car_type,
    smart_search: searchForm.smart_search,
    inquiryMethod: inquiryMethod.value
  }
  if (import.meta.client) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(formData))
  }
}

// 从本地存储恢复表单数据
const restoreFormData = async () => {
  if (!import.meta.client) return

  const savedData = localStorage.getItem(STORAGE_KEY)
  if (savedData) {
    try {
      const formData = JSON.parse(savedData)
      const savedBrand = formData.f_brand || ''
      const savedSeries = formData.f_series || ''
      const savedModel = formData.f_car_model || ''
      const savedType =
        formData.car_type || $t('generateQuote.manualMode.carType')
      const savedSmartSearch = formData.smart_search || ''
      const savedInquiryMethod =
        formData.inquiryMethod || $t('generateQuote.manualMode.title')

      // 恢复询价方式
      inquiryMethod.value = savedInquiryMethod

      if (savedInquiryMethod === $t('generateQuote.manualMode.title')) {
        // 如果有品牌数据，先加载车系数据
        if (savedBrand) {
          await fetchSeries(Number(savedBrand))
          // 车系数据加载完成后，设置品牌和车系
          searchForm.f_brand = savedBrand
          searchForm.f_series = savedSeries

          // 如果有车系数据，加载车型数据
          if (savedSeries) {
            await fetchModels(Number(savedSeries))
            // 车型数据加载完成后，设置车型
            searchForm.f_car_model = savedModel
          }
        }

        // 设置车辆类型
        searchForm.car_type = savedType
      } else {
        // 智能找车模式
        searchForm.smart_search = savedSmartSearch
        searchForm.car_type = savedType
      }

      // 恢复数据后立即清除本地存储
      clearFormData()
    } catch (error) {
      console.error('恢复表单数据失败:', error)
    }
  }
}

// 清除本地存储的表单数据
const clearFormData = () => {
  if (import.meta.client) {
    localStorage.removeItem(STORAGE_KEY)
  }
}

// 获取车系数据
const fetchSeries = async (brandId: number) => {
  seriesLoading.value = true
  try {
    const response = (await getCarSeries(brandId)) as any
    seriesOptions.value = response.data || []
  } catch (error) {
    ElMessage.error($t('generateQuote.errors.fetchSeriesFailed'))
    console.error('获取车系数据失败:', error)
  } finally {
    seriesLoading.value = false
  }
}

// 获取车型数据
const fetchModels = async (seriesId: number) => {
  modelLoading.value = true
  try {
    const response = (await getCarModel(seriesId)) as any
    modelOptions.value = response.data || []
  } catch (error) {
    ElMessage.error($t('generateQuote.errors.fetchModelsFailed'))
    console.error('获取车型数据失败:', error)
  } finally {
    modelLoading.value = false
  }
}

// 品牌变化处理
const handleBrandChange = async (brandId: number) => {
  // 清空车系和车型选择
  searchForm.f_series = ''
  searchForm.f_car_model = ''
  seriesOptions.value = []
  modelOptions.value = []

  if (brandId) {
    await fetchSeries(brandId)
  }
}

// 车系变化处理
const handleSeriesChange = async (seriesId: number) => {
  // 清空车型选择
  searchForm.f_car_model = ''
  modelOptions.value = []

  if (seriesId) {
    await fetchModels(seriesId)
  }
}

// 处理手动选车模式生成报价单按钮点击
const handleManualQuote = async () => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    // 保存当前表单数据并跳转到登录页
    saveFormData()
    await navigateTo('/login')
    return
  }

  // 手动选车模式验证
  if (!searchForm.f_brand) {
    ElMessage.warning($t('generateQuote.validation.brandRequired'))
    return
  }

  if (!searchForm.f_series) {
    ElMessage.warning($t('generateQuote.validation.seriesRequired'))
    return
  }

  // 生成报价单
  await generateManualQuote()
}

// 处理智能找车模式生成报价单按钮点击
const handleSmartQuote = async () => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    // 保存当前表单数据并跳转到登录页
    saveFormData()
    await navigateTo('/login')
    return
  }

  // 智能找车模式验证
  if (!searchForm.smart_search || searchForm.smart_search.trim() === '') {
    ElMessage.warning($t('generateQuote.validation.smartSearchRequired'))
    return
  }

  // 生成报价单
  await generateSmartQuote()
}

// 生成手动选车模式报价单
const generateManualQuote = async () => {
  try {
    loading.value = true

    // 手动选车模式：使用具体的品牌、车系、车型信息
    const orderReserve = [
      {
        f_brand: searchForm.f_brand || null,
        f_series: searchForm.f_series || null,
        f_car_model: searchForm.f_car_model || null,
        number: 1,
        car_type: searchForm.car_type || null
      }
    ]

    const resOrders = await createOrders(
      {
        f_sales: userStore.userInfo?.id || null, // 所属销售
        f_belong_company: userStore.userInfo?.f_belong_company || null, // 所属公司
        automatic_inquiry: '是',
        orders_status: '询价订单',
        method_of_inquiry: $t('generateQuote.manualMode.title'),
        order_reserve: orderReserve
      },
      'oiuvkrv404r'
    )

    ordersId.value = resOrders.data.id

    // 轮询获取推荐车源，有数据即停止（最多 10 次）
    await pollRecommendCars(resOrders.data.id, {
      interval: 3000,
      maxAttempts: 10
    })

    generateSuccess.value = true
    ElMessage.success($t('generateQuote.messages.success'))
  } catch (error) {
    loading.value = false
    console.error('生成报价单失败:', error)
    ElMessage.error($t('generateQuote.messages.error'))
  }
}

// 生成智能找车模式报价单
const generateSmartQuote = async () => {
  try {
    loading.value = true

    const resOrders = await createOrders(
      {
        f_sales: userStore.userInfo?.id || null, // 所属销售
        f_belong_company: userStore.userInfo?.f_belong_company || null, // 所属公司
        automatic_inquiry: '否',
        orders_status: '询价订单',
        method_of_inquiry: $t('generateQuote.smartMode.title'),
        requirements_analysis: searchForm.smart_search
      },
      'wgsbpgpkdoe'
    )

    ordersId.value = resOrders.data.id

    // 轮询获取订单明细，如果有数据则停止（最多 10 次）
    for (let i = 0; i < 10; i++) {
      if (isUnmounted.value) break
      // 调用 getOrderDetailForQuote 获取订单明细
      const resDetail = await getOrderDetailForQuote(resOrders.data.id)
      // 如果返回的数据存在且长度大于0，则停止轮询
      if (Array.isArray(resDetail?.data) && resDetail.data.length > 0) {
        await updateOrders({
          id: resOrders.data.id,
          automatic_inquiry: '是'
        })
        break
      }
      // 如果还没到最大次数，则等待3秒
      if (i < 9) {
        await sleep(3000)
      }
    }

    // 轮询获取推荐车源，有数据即停止（最多 10 次）
    await pollRecommendCars(resOrders.data.id, {
      interval: 3000,
      maxAttempts: 10
    })

    generateSuccess.value = true
    ElMessage.success($t('generateQuote.messages.success'))
  } catch (error) {
    loading.value = false
    console.error('生成报价单失败:', error)
    ElMessage.error($t('generateQuote.messages.error'))
  }
}

const handleGoToAdmin = () => {
  const config = useRuntimeConfig()
  const tokenCookie = useCookie<string>('token', { default: () => '' })
  const token = tokenCookie.value || ''
  const erpUrl = config.public.sellwellautoApiBase.replace('/api', '') // 移除 /api 后缀获取ERP基础URL
  const redirectUrl = ordersId.value
    ? `${erpUrl}/admin/szsyytefw7c/popups/oti0hnjnr0t/filterbytk/${ordersId.value}`
    : `${erpUrl}/admin/szsyytefw7c`

  if (import.meta.client) {
    window.open(
      `${erpUrl}/quick-login.html?token=${encodeURIComponent(token)}&redirectUrl=${redirectUrl}`,
      '_blank'
    )
  }
  loading.value = false
  generateSuccess.value = false
}

// 简化版轮询（最多尝试 N 次，间隔固定）
const pollRecommendCars = async (
  ordersId: number,
  options?: { interval?: number; maxAttempts?: number }
): Promise<any | null> => {
  const interval = options?.interval ?? 3000
  const maxAttempts = options?.maxAttempts ?? 10
  for (let i = 0; i < maxAttempts; i++) {
    if (isUnmounted.value) return null
    try {
      const res = await getRecommendCars({ ordersId })
      if (Array.isArray(res?.data) && res.data.length > 0) return res
    } catch (error) {
      console.error('获取推荐车源失败:', error)
      // 忽略错误
    }
    if (i < maxAttempts - 1) {
      await sleep(interval)
    }
  }

  return null
}

const sleep = (ms: number) => new Promise<void>((r) => setTimeout(r, ms))

// 组件卸载时标记中止
onBeforeUnmount(() => {
  isUnmounted.value = true
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.generate-quote-container {
  position: relative;
  transform: translateY(-80px);
  min-height: 200px;
  z-index: 20;
}

.quote-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 40px 30px 30px 30px;
  max-width: $max-width;
  margin: 0 auto;
  position: relative;
}

.orders-type-switch {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);

  .switch-container {
    display: flex;
    background: #f5f5f5;
    border-radius: 25px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    position: relative;
    overflow: hidden;

    .switch-slider {
      position: absolute;
      top: 2.5px;
      left: 2px;
      width: calc(50% - 4px);
      height: calc(100% - 6px);
      background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
      border-radius: 40px;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 1;

      &.slide-right {
        transform: translateX(calc(100% + 4px));
      }
    }

    .switch-option {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      border-radius: 21px;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 100px;
      justify-content: center;
      position: relative;
      overflow: hidden;
      z-index: 2;
      flex: 1;

      &.active {
        color: white;
        font-weight: 600;

        .switch-icon {
          color: white;
        }

        .switch-text {
          color: white;
          font-weight: 600;
        }
      }

      &:not(.active) {
        color: #666;

        .switch-icon {
          color: #999;
        }

        .switch-text {
          color: #666;
        }
      }

      .switch-icon {
        font-size: 18px;
        transition: color 0.3s ease;
      }

      .switch-text {
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        white-space: nowrap;
      }
    }
  }
}

.input-section {
  margin-bottom: 30px;
}

.quote-form {
  .form-row {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
  }

  .form-item {
    margin-bottom: 0;
    flex: 1;
    min-width: 150px;
  }

  .form-select {
    width: 100%;
  }

  .form-input {
    width: 100%;
  }

  .generate-btn {
    background: linear-gradient(135deg, $primary-color 0%, #f7931e 100%);
    border: none;
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    min-width: 140px;
    height: 40px;
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;

    &:hover {
      background: linear-gradient(135deg, #e55a2b 0%, #e0851a 100%);
      box-shadow: 0 10px 20px rgba(255, 107, 53, 0.4);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s ease;
    }

    &:hover::before {
      left: 100%;
    }
  }
}

// loading 弹窗样式
.loading-modal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 0 8px;
}

.loading-modal__icon {
  color: var(--el-color-primary);
  margin-bottom: 10px;
}

.loading-modal__text {
  font-size: 14px;
  color: #606266;
}

.success-text {
  margin-top: 10px;
}

.success-text-link {
  color: $primary-color;
  cursor: pointer;
}

// 响应式设计
@media (max-width: 768px) {
  .generate-quote-container {
    padding: 10px;
  }

  .quote-card {
    padding: 20px;
  }

  .quote-form .form-row {
    gap: 10px;
  }

  .form-item {
    min-width: auto;
  }

  .generate-btn {
    width: 100%;
  }

  .hot-rank-list {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .separator {
    display: none;
  }
}

.el-select-dropdown__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  font-size: 20px;
}

.circular {
  display: inline;
  height: 30px;
  width: 20px;
  animation: loading-rotate 2s linear infinite;
  border-radius: 4px;
}

.path {
  animation: loading-dash 1.5s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: var(--el-color-primary);
  stroke-linecap: round;
}

.loading-path .dot1 {
  transform: translate(3.75px, 3.75px);
  fill: var(--el-color-primary);
  animation: custom-spin-move 1s infinite linear alternate;
  opacity: 0.3;
}

.loading-path .dot2 {
  transform: translate(calc(100% - 3.75px), 3.75px);
  fill: var(--el-color-primary);
  animation: custom-spin-move 1s infinite linear alternate;
  opacity: 0.3;
  animation-delay: 0.4s;
}

.loading-path .dot3 {
  transform: translate(3.75px, calc(100% - 3.75px));
  fill: var(--el-color-primary);
  animation: custom-spin-move 1s infinite linear alternate;
  opacity: 0.3;
  animation-delay: 1.2s;
}

.loading-path .dot4 {
  transform: translate(calc(100% - 3.75px), calc(100% - 3.75px));
  fill: var(--el-color-primary);
  animation: custom-spin-move 1s infinite linear alternate;
  opacity: 0.3;
  animation-delay: 0.8s;
}

@keyframes loading-rotate {
  to {
    transform: rotate(360deg);
  }
}

@keyframes loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40px;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120px;
  }
}

@keyframes custom-spin-move {
  to {
    opacity: 1;
  }
}
</style>
