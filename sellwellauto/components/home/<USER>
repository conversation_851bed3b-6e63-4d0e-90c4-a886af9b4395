<template>
  <section class="banner-section">
    <Swiper
      :modules="[Autoplay, Pagination, Navigation]"
      :slides-per-view="1"
      :loop="true"
      :autoplay="{
        delay: 4000,
        disableOnInteraction: false,
      }"
      :navigation="{
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
      }"
      :speed="800"
      class="banner-swiper"
    >
      <SwiperSlide v-for="banner in bannerImages" :key="banner.id" class="banner-slide">
        <div class="banner-content">
          <img :src="banner.src" class="banner-image" />
        </div>
      </SwiperSlide>

      <!-- 自定义导航按钮 -->
      <div class="swiper-button-prev banner-nav-btn" />
      <div class="swiper-button-next banner-nav-btn" />
    </Swiper>
  </section>
</template>

<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Pagination, Navigation } from 'swiper/modules'

// 导入Swiper样式
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'

// 轮播图数据
const bannerImages = [
  {
    id: 1,
    src: '/images/home/<USER>',
  },
  {
    id: 2,
    src: '/images/aboutSellwellauto/banner.png',
  },
]
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;
@use '@/assets/styles/mixins' as *;

.banner-section {
  width: 100%;
  height: 40vh;
  position: relative;
}

.banner-swiper {
  width: 100%;
  height: 40vh;

  :deep(.swiper-slide) {
    width: 100%;
    height: 40vh;
  }
}

.banner-slide {
  width: 100%;
  height: 40vh;
}

.banner-content {
  position: relative;
  width: 100%;
  height: 40vh;
}

.banner-image {
  width: 100%;
  height: 40vh;
  object-fit: cover;
}

// 自定义导航按钮样式
.banner-nav-btn {
  color: white !important;
  position: absolute !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition:
    transform 0.3s ease,
    font-size 0.3s ease;
  cursor: pointer;

  &::after {
    font-size: 28px;
    font-weight: bold !important;
    transition: font-size 0.3s ease;
  }

  &:hover {
    transform: translateY(-50%) scale(1.1);

    &::after {
      font-size: 32px;
    }
  }
}

// 左侧按钮定位
.swiper-button-prev {
  left: 120px !important;
}

// 右侧按钮定位
.swiper-button-next {
  right: 120px !important;
}

// 响应式调整
@include respond-to(sm) {
  .banner-nav-btn {
    &::after {
      font-size: 16px !important;
    }
  }
}
</style>
