<template>
  <div class="hot-rank-card">
    <!-- 标题 -->
    <div class="hot-rank-title">{{ $t('hotRankMarquee.title') }}</div>

    <!-- 跑马灯区域 -->
    <div class="marquee-container">
      <img src="/images/home/<USER>" :alt="$t('hotRankMarquee.title')" class="hot-rank-bg" />
      <div class="css-marquee">
        <div class="marquee-content">
          <!-- 第一组数据 -->
          <div v-for="(item, index) in hotRankData" :key="`first-${index}`" class="marquee-item">
            <span class="car-name">{{ item }}</span>
            <span class="separator">|</span>
          </div>
          <!-- 第二组数据（用于无缝循环） -->
          <div v-for="(item, index) in hotRankData" :key="`second-${index}`" class="marquee-item">
            <span v-if="index === 0" class="fire-icon">🔥</span>
            <span class="car-name">{{ item }}</span>
            <span class="separator">|</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 纯静态组件，无需响应式数据

// 热力榜数据
const hotRankData = [
  '大众 探歌2022款 280TSI DSG 两驱舒享PLUS',
  '大众 探歌2023款 300TSI DSG 星光版',
  '大众 探影2021款 200TSI DSG 悦智联版',
  '大众 探影2023款 200TSI DSG 悦智联版',
  '大众 高尔夫2023款 280TSI DSG R-Line Lite',
  '大众 高尔夫2023款 改款 280TSI DSG PRO',
  '大众 途铠2023款 300TSI DSG 豪华版',
  '大众 帕萨特2024款 1.4TSI 2800TSI星空精英版 自动挡',
  '大众 帕萨特2023款 330TSI星空精英版（改款） 自动挡',
  '奇瑞 捷途X70 PLUS 2025款 1.5T DCT 豪华版 7座',
  '奇瑞 捷途X70 PLUS 2023款 1.5T DCT 勇者PLUS 5座',
  '奇瑞 捷途大圣 2025款 1.5T DCT 超越版',
  '奇瑞 捷途山海T2 2024款 1.5TD DHT  129KM 两驱林野版',
  '起亚 K3 2023款 1.5L IVT 豪华版',
  '睿蓝 睿蓝X3PRO 2022款 1.5L 手动小萌',
  '睿蓝 睿蓝X3PRO 2022款 1.5L CVT小飒',
  '丰田 卡罗拉锐放 2023款 2.0L 智能电混双擎先锋版',
  '丰田 卡罗拉2024款 1.2T 先锋版',
  '丰田 雷凌 2019款双擎 1.8H ECVT 豪华版',
  '丰田 YARIS L 致炫 2022款 致炫X 1.5L CVT 领先PLUS版',
  '奥迪 奥迪A3 2023款 SPORTBACK 35TFSI 豪华运动型（改款）自动挡',
  '奥迪 奥迪A3 2022款 SPORTBACK 35TFSI 豪华运动型 自动挡',
  '比亚迪 方程豹 豹5',
  '吉利 帝豪 2022款 第四代 1.5L CVT旗舰型',
  '吉利 缤越 2023款 1.5T DCT铂金版',
]

// 纯 CSS 处理悬停效果，无需 JavaScript
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;
@use '@/assets/styles/mixins' as *;

.hot-rank-title {
  font-size: 12px;
  font-weight: 600;
  color: #ccc;
  margin-bottom: 4px;
  text-align: left;
}

.marquee-container {
  background: #f6f6f6;
  border-radius: 8px;
  padding: 2px 16px;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  @include flex-center;
  gap: 8px;
}

// CSS 跑马灯效果
.css-marquee {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;

  .marquee-content {
    display: inline-flex;
    align-items: center;
    animation: scroll 120s linear infinite;
  }
}

// 鼠标悬停时暂停动画（纯CSS，立即生效）
.marquee-container:hover .css-marquee .marquee-content {
  animation-play-state: paused;
}

// 滚动动画
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.marquee-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  white-space: nowrap;
  transition: all 0.3s ease;
  margin-right: 4px;

  &:hover {
    background: #f8f8f8;
    border-color: #ddd;
  }
}

.fire-icon {
  color: $primary-color;
  font-size: 16px;
}

.car-name {
  color: #bdbdbd;
  font-size: 14px;
  font-weight: 500;
}

.separator {
  color: #ddd;
  font-weight: 300;
  margin-left: 10px;
}

// 响应式设计
@media (max-width: 768px) {
  .hot-rank-card {
    padding: 15px;
    margin: 15px 0;
  }

  .hot-rank-title {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .marquee-container {
    padding: 12px;
  }

  .marquee-item {
    padding: 6px 10px;
    gap: 6px;
  }

  .car-name {
    font-size: 13px;
  }

  .separator {
    margin-left: 8px;
  }
}
</style>
