<template>
  <section class="faq-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">{{ $t('home.faq.title') }}</h2>
      </div>

      <div class="faq-grid">
        <div class="faq-column">
          <el-collapse v-model="activeNames">
            <el-collapse-item
              v-for="(faq, index) in leftColumnFaqs"
              :key="index"
              :name="index"
              class="faq-item"
            >
              <template #title>
                <span class="faq-question">
                  <span class="question-dot" />
                  {{ faq.question }}
                </span>
              </template>
              <div class="faq-answer">
                <p>{{ faq.answer }}</p>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div class="faq-column">
          <el-collapse v-model="activeNames2">
            <el-collapse-item
              v-for="(faq, index) in rightColumnFaqs"
              :key="index"
              :name="index"
              class="faq-item"
            >
              <template #title>
                <span class="faq-question">
                  <span class="question-dot" />
                  {{ faq.question }}
                </span>
              </template>
              <div class="faq-answer">
                <p>{{ faq.answer }}</p>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const { t } = useI18n()

const activeNames = ref([])
const activeNames2 = ref([])

const faqList = computed(() => [
  {
    question: t('home.faq.questions.noExperience.question'),
    answer: t('home.faq.questions.noExperience.answer'),
  },
  {
    question: t('home.faq.questions.overseasVerification.question'),
    answer: t('home.faq.questions.overseasVerification.answer'),
  },
  {
    question: t('home.faq.questions.pricingProcess.question'),
    answer: t('home.faq.questions.pricingProcess.answer'),
  },
  {
    question: t('home.faq.questions.carSourceUpload.question'),
    answer: t('home.faq.questions.carSourceUpload.answer'),
  },
  {
    question: t('home.faq.questions.thirdPartyServices.question'),
    answer: t('home.faq.questions.thirdPartyServices.answer'),
  },
  {
    question: t('home.faq.questions.customQuote.question'),
    answer: t('home.faq.questions.customQuote.answer'),
  },
])

const leftColumnFaqs = computed(() => {
  return faqList.value.filter((_, index) => index % 2 === 0)
})

const rightColumnFaqs = computed(() => {
  return faqList.value.filter((_, index) => index % 2 === 1)
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;
@use '@/assets/styles/mixins' as *;

.faq-section {
  padding: 80px 0;
}

.container {
  max-width: $max-width;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.faq-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.faq-column {
  display: flex;
  flex-direction: column;
}

.faq-item {
  margin-bottom: 20px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }
}

.faq-question {
  font-size: 1.1rem;
  font-weight: 600;
  color: #999;
  line-height: 1.5;
  display: flex;
  align-items: center;
  gap: 8px;
}

.question-dot {
  width: 6px;
  height: 6px;
  background-color: #999;
  border-radius: 50%;
  flex-shrink: 0;
}

.faq-answer {
  padding: 20px 0;
  color: #666;
  line-height: 1.6;
}

:deep(.el-collapse) {
  border: none !important;
}

:deep(.el-collapse-item__header) {
  padding: 25px 30px;
  background: white;
  border: none;
  font-size: 1.1rem;
  font-weight: 600;

  &:hover {
    background: $background-color-base;
  }
}

:deep(.el-collapse-item__content) {
  padding: 0 30px 25px;
  background: white;
  border: none;
}

:deep(.el-collapse-item__arrow) {
  color: $primary-color;
  font-size: 18px;
}

@media (max-width: 768px) {
  .faq-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .section-title {
    font-size: 2rem;
  }

  .faq-question {
    font-size: 1rem;
  }

  :deep(.el-collapse-item__header) {
    padding: 20px;
  }

  :deep(.el-collapse-item__content) {
    padding: 0 20px 20px;
  }
}
</style>
