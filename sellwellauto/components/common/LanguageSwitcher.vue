<template>
  <div class="language-switcher">
    <el-dropdown trigger="hover" @command="changeLanguage">
      <span class="language-trigger">
        {{ currentLanguage?.name }}
        <el-icon class="el-icon--right"><ArrowDown /></el-icon>
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="lang in availableLanguages"
            :key="lang.code"
            :command="lang.code"
            :class="{ active: lang.code === locale }"
          >
            <span class="dropdown-item-content">
              <span class="lang-name">{{ lang.name }}</span>
            </span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { ArrowDown } from '@element-plus/icons-vue'

const { currentLanguage, availableLanguages, locale, changeLanguage } =
  useI18nHelper()
</script>

<style scoped lang="scss">
.language-switcher {
  .language-trigger {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s;
    color: white;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}
</style>
