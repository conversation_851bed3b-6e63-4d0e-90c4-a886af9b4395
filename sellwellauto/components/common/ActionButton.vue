<template>
  <button
    class="action-btn"
    :class="[`action-btn--${props.size}`, `action-btn--${variant}`, customClass]"
    :disabled="loading"
    @click="handleClick"
  >
    <span v-if="loading" class="loading-text">{{ loadingText }}</span>
    <span v-else>{{ text }}</span>
  </button>
</template>

<script setup lang="ts">
interface Props {
  text?: string // 按钮文字
  size?: 'small' | 'medium' | 'large' // 按钮大小
  variant?: 'default' | 'primary' | 'outline' // 按钮样式变体
  customClass?: string // 自定义样式类
  loadingText?: string // 加载时的文字
  actionType?: 'register' | 'trial' | 'contact' | 'custom' // 按钮类型
}

const props = withDefaults(defineProps<Props>(), {
  text: '立即注册',
  size: 'small',
  variant: 'primary',
  customClass: '',
  loadingText: '处理中...',
  actionType: 'register',
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const userStore = useUserStore()
const config = useRuntimeConfig()

const loading = computed(() => false) // 可以根据需要添加loading状态

const handleClick = async (event: MouseEvent) => {
  emit('click', event)

  // 如果用户已登录，直接跳转到后台
  if (userStore.isLoggedIn) {
    const token = localStorage.getItem('token') || ''
    if (import.meta.client) {
      window.open(
        `${config.public.erpUrl}/quick-login.html?token=${encodeURIComponent(token)}`,
        '_blank'
      )
    }
    return
  }

  // 如果用户未登录，跳转到登录页
  navigateTo('/login')
}
</script>

<style lang="scss" scoped>
.action-btn {
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  outline: none;

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  // 小尺寸
  &--small {
    padding: 6px 16px;
    font-size: 0.875rem;
    min-height: 32px;
  }

  // 中等尺寸（默认）
  &--medium {
    padding: 8px 24px;
    font-size: 1rem;
    min-height: 40px;
  }

  // 大尺寸
  &--large {
    padding: 12px 32px;
    font-size: 1.125rem;
    min-height: 48px;
  }

  // 主要样式（默认）
  &--primary {
    background: $primary-color;
    color: white;

    &:hover {
      background: #e55a2b;
    }
  }

  // 默认样式
  &--default {
    background: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;

    &:hover {
      background: #e8e8e8;
    }
  }

  // 轮廓样式
  &--outline {
    background: transparent;
    color: $primary-color;
    border: 2px solid $primary-color;

    &:hover {
      background: $primary-color;
      color: white;
    }
  }
}

.loading-text {
  opacity: 0.8;
}
</style>
