<template>
  <span :class="flagClass" :title="title" :style="computedStyle" />
</template>

<script setup lang="ts">
interface Props {
  country: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  squared?: boolean
  title?: string
  customStyle?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  squared: false,
  title: '',
  customStyle: () => ({})
})

// 国家代码映射
const countryCodeMap: Record<string, string> = {
  'zh-CN': 'cn',
  'en-US': 'us',
  zh: 'cn',
  en: 'us'
}

// 尺寸映射
const sizeMap: Record<string, string> = {
  sm: '16px',
  md: '20px',
  lg: '24px',
  xl: '32px'
}

// 计算国旗类名
const flagClass = computed(() => {
  // 首先尝试直接匹配，然后尝试小写匹配
  const countryCode =
    countryCodeMap[props.country] ||
    countryCodeMap[props.country.toLowerCase()] ||
    props.country.toLowerCase()
  const baseClass = 'fi'
  const countryClass = `fi-${countryCode}`
  const squaredClass = props.squared ? 'fis' : ''

  return [baseClass, countryClass, squaredClass].filter(Boolean).join(' ')
})

// 计算样式
const computedStyle = computed(() => ({
  fontSize: sizeMap[props.size],
  ...props.customStyle
}))
</script>

<style scoped>
.fi {
  display: inline-block;
  width: 1.33333333em;
  line-height: 1em;
  vertical-align: middle;
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
}

.fis {
  border-radius: 0.25em;
}
</style>
