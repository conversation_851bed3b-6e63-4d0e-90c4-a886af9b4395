<template>
  <div class="sidebar" :class="{ 'sidebar--hidden': !isVisible }">
    <div class="sidebar-container">
      <!-- 立即咨询 -->
      <el-popover
        placement="left"
        :width="120"
        trigger="hover"
        popper-class="sidebar-popover"
        :show-arrow="false"
      >
        <template #reference>
          <div class="sidebar-section">
            <div class="sidebar-icon">
              <svg
                t="1754904144555"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="19001"
                width="18"
                height="18"
              >
                <path
                  d="M435.127 73.874c236.763 0 431.982 160.549 431.982 363.74l-0.293-13.824a95.67 95.67 0 0 0-98.743-21.212C746.79 273.115 607.378 169.984 435.2 169.984c-187.392 0-336.018 122.149-336.018 267.703 0 74.898 51.053 157.769 128.585 212.553 6.583 4.681 11.849 10.825 15.36 17.993 5.924 12.069 8.046 29.404 6.51 55.808l-1.829 21.577c-0.805 7.827-1.902 16.238-3.145 25.308l-1.097 7.168 55.223-27.575 29.769-14.41 23.405-10.824 12.581-5.34 10.46-4.096 8.557-2.852 7.022-1.756a44.544 44.544 0 0 1 5.632-0.804l115.785-0.22 6.73-0.073a95.89 95.89 0 0 0 41.252 92.745c-15.287 2.268-31.452 3.292-47.982 3.292H405.797l-12.069 5.339-34.523 16.384-45.788 22.601-111.323 56.393a47.982 47.982 0 0 1-69.12-51.42l3.437-18.65 5.852-34.012 4.754-29.623 3.584-25.307 1.975-17.262 1.317-15.945-8.339-6.583C63.634 640.586 8.777 546.743 3.511 452.462l-0.366-14.848c0-203.191 195.292-363.74 431.982-363.74zM912.09 766.025c-25.307 20.188-52.224 43.154-58.806 35.548-10.021-7.827 12.946-32.403 28.013-51.932 12.58-19.236 26.77-38.912 26.77-62.903a57.637 57.637 0 1 1 57.93 57.49c-18.652 0-37.45 11.191-53.833 21.797z"
                  fill="#ffffff"
                  p-id="19002"
                />
                <path
                  d="M797.77 578.999a64.146 64.146 0 0 1-56.54-37.596c-8.924-21.577-7.022-44.982 12.58-63.341 19.749-18.286 44.471-18.944 65.975-10.02 21.577 8.923 35.62 29.988 35.62 53.32 0 20.041 11.045 37.45 21.505 53.98 25.16 30.72 45.129 50.907 35.255 59.1-4.389 5.265-31.818-13.532-51.566-28.307-19.237-12.727-40.814-27.063-62.903-27.063z m-103.79 40.009c20.48-15.726 46.591-37.595 54.49-31.378 6.73 4.315-13.019 32.621-28.013 51.931-12.654 19.164-22.747 26.917-26.478 60.343-3.657 33.5-26.55 61.074-58.44 61.147a59.611 59.611 0 0 1-59.393-58.148c-0.073-31.817 29.038-58.588 59.392-58.588 30.428 0 42.057-14.701 58.515-25.307z m108.178 192c23.405 0 46.445 12.58 55.442 34.085a59.538 59.538 0 0 1-12.727 63.926 60.855 60.855 0 0 1-64.731 12.947 57.637 57.637 0 0 1-35.62-53.321 156.38 156.38 0 0 0-21.359-54.126c-15.506-21.797-44.397-48.64-34.962-59.246 5.632-10.24 32.037 10.606 51.42 28.745 19.017 12.435 37.595 22.528 62.537 26.99z"
                  fill="#ffffff"
                  p-id="19003"
                />
              </svg>
            </div>
            <!-- <div class="sidebar-text">企微</div> -->
          </div>
        </template>

        <div class="popover-content">
          <div class="qr-code-placeholder">
            <div class="">{{ $t('sidebar.wechatWork') }}</div>
            <img src="/images/home/<USER>" :alt="$t('sidebar.wechatWork')" />
          </div>
        </div>
      </el-popover>

      <!-- 分隔线 -->
      <div class="sidebar-separator" />

      <!-- 关注我们 -->
      <el-popover
        placement="left"
        :width="280"
        trigger="hover"
        popper-class="sidebar-popover sidebar-popover--social"
        :show-arrow="false"
      >
        <template #reference>
          <div class="sidebar-section">
            <div class="sidebar-icon">
              <svg
                t="1754904119269"
                class="icon"
                viewBox="0 0 1163 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="14435"
                width="18"
                height="18"
              >
                <path
                  d="M582.567972 1024c-29.037618 0-58.075237-14.518809-79.853451-29.037618L103.447267 595.695128c-137.928688-137.928688-137.928688-355.710826 0-493.639514 14.518809-14.518809 36.297023-29.037618 58.075237-43.556428C292.191787-28.613669 466.417498-14.09486 582.567972 87.536805 698.718446-14.09486 872.944157-28.613669 1003.61344 51.239782c159.706902 101.631665 210.522734 319.413803 101.631664 479.120704-14.518809 21.778214-29.037618 36.297023-43.556427 58.075237l-399.267254 399.267254c-21.778214 29.037618-50.815832 36.297023-79.853451 36.297023zM343.00762 145.612042c-36.297023 0-72.594046 7.259405-108.891069 29.037618-43.556428 36.297023-79.853451 87.112855-87.112856 152.447497-7.259405 58.075237 14.518809 123.409878 58.075237 166.966306L582.567972 864.293098l377.48904-377.48904c43.556428-43.556428 65.334642-101.631665 58.075237-166.966306-7.259405-58.075237-43.556428-116.150474-94.37226-152.447497-79.853451-50.815832-188.74452-36.297023-261.338566 36.297023L582.567972 290.800134 495.455117 210.946683C459.158094 167.390255 401.082857 145.612042 343.00762 145.612042z"
                  fill="#ffffff"
                  p-id="14436"
                />
              </svg>
            </div>
            <!-- <div class="sidebar-text">公众号</div>
            <div class="sidebar-text">抖音</div> -->
          </div>
        </template>

        <div class="popover-content">
          <div class="social-qr-codes">
            <div class="social-item">
              <div class="qr-code-placeholder">
                <div class="">{{ $t('sidebar.wechatOfficialAccount') }}</div>
                <img
                  src="/images/home/<USER>"
                  :alt="$t('sidebar.wechatOfficialAccount')"
                  class="social-icon"
                />
              </div>
            </div>
            <div class="social-item">
              <div class="qr-code-placeholder">
                <div class="">{{ $t('sidebar.douyin') }}</div>
                <img src="/images/home/<USER>" :alt="$t('sidebar.douyin')" class="social-icon" />
              </div>
            </div>
          </div>
        </div>
      </el-popover>

      <!-- 分隔线 -->
      <div class="sidebar-separator" />

      <!-- 返回顶部 -->
      <div class="sidebar-section sidebar-section--top" @click="scrollToTop">
        <div class="sidebar-icon">
          <svg
            t="*************"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="20201"
            width="18"
            height="18"
          >
            <path
              d="M554.666667 268.8v601.6h-85.333334V268.8L337.066667 401.066667 277.333333 341.333333 512 106.666667 746.666667 341.333333l-59.733334 59.733334L554.666667 268.8z"
              fill="#ffffff"
              p-id="20202"
            />
          </svg>
        </div>
        <span class="sidebar-text">{{ $t('sidebar.top') }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 响应式数据
const isVisible = ref(true)

// 返回顶部
const scrollToTop = () => {
  if (import.meta.client) {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  }
}
</script>

<style lang="scss" scoped>
.sidebar {
  position: fixed;
  right: 16px;
  bottom: -60px;
  transform: translateY(-50%);
  z-index: 1000;
  transition: all 0.3s ease;

  &--hidden {
    opacity: 0;
    pointer-events: none;
  }

  @include respond-to(sm) {
    right: 10px;
  }
}

.sidebar-container {
  background: rgba(255, 107, 53, 0.9);
  border-radius: 55px;
  padding: 14px 10px;
  box-shadow: $box-shadow-light;
  transition: all 0.3s ease;
  min-width: 55px;
  backdrop-filter: blur(10px);

  &:hover {
    transform: scale(1.02);
    box-shadow: $box-shadow-dark;
    background: rgba(255, 107, 53, 0.95);
  }

  @include respond-to(sm) {
    padding: 12px 8px;
    min-width: 50px;
  }
}

.sidebar-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 0px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    .sidebar-icon {
      transform: scale(1.2);
      color: rgba(255, 255, 255, 0.9);
    }
  }

  &--top {
    &:hover {
      .sidebar-icon {
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }

  @include respond-to(sm) {
    padding: 10px 6px;
  }
}

.sidebar-text {
  font-size: 14px;
  color: white;
}

.sidebar-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: white;
  margin-bottom: 6px;

  .icon {
    width: 100%;
    height: 100%;
  }

  @include respond-to(sm) {
    width: 18px;
    height: 18px;
    margin-bottom: 4px;
  }
}

.sidebar-separator {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.3);
  margin: 4px 0;

  @include respond-to(sm) {
    margin: 3px 0;
  }
}

// 弹窗内容样式
.popover-content {
  h4 {
    margin: 0 0 16px 0;
    font-size: $font-size-base;
    color: $text-primary;
    text-align: center;
    font-weight: 600;
  }
}

.qr-code-placeholder {
  border-radius: $border-radius-base;
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: $text-secondary;
  text-align: center;

  img {
    width: 120px;
    height: 120px;
    object-fit: fill;
    border-radius: $border-radius-base;
    margin-top: 4px;
  }

  @include respond-to(sm) {
    img {
      width: 100px;
      height: 100px;
    }
  }
}

.qr-code-text {
  font-size: $font-size-small;
  margin-bottom: 4px;
}

.social-qr-codes {
  display: flex;
  gap: 16px;
  justify-content: center;

  @include respond-to(sm) {
    gap: 12px;
  }
}

.social-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.social-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;

  @include respond-to(sm) {
    width: 20px;
    height: 20px;
  }
}

// 响应式隐藏
@include respond-to(xs) {
  .sidebar {
    display: none;
  }
}
</style>

<style lang="scss">
// 全局弹窗样式
.sidebar-popover {
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;

  &.sidebar-popover--social {
    .el-popover__title {
      margin-bottom: 16px;
      font-size: 16px;
      color: #333;
      text-align: center;
      font-weight: 600;
    }
  }

  .el-popover__title {
    margin-bottom: 16px;
    font-size: 16px;
    color: #333;
    text-align: center;
    font-weight: 600;
  }
}
</style>
