<template>
  <div class="form-content">
    <div class="form-header">
      <h2 class="form-title">{{ $t('login.registerForm.title') }}</h2>
      <p class="form-subtitle">{{ $t('login.registerForm.subtitle') }}</p>
    </div>

    <el-form
      ref="registerFormRef"
      :model="registerForm"
      :rules="registerRules"
      class="register-form"
      @submit.prevent="handleRegister"
    >
      <!-- 中文模式下显示手机号+验证码注册 -->
      <template v-if="isZhCN">
        <el-form-item prop="phone">
          <el-input
            v-model="registerForm.phone"
            :placeholder="$t('login.registerForm.phone.placeholder')"
            size="large"
            prefix-icon="Phone"
            clearable
            maxlength="11"
          />
        </el-form-item>

        <el-form-item prop="code">
          <div class="code-input-group">
            <el-input
              ref="codeInputRef"
              v-model="registerForm.code"
              :placeholder="$t('login.registerForm.code.placeholder')"
              size="large"
              prefix-icon="Key"
              maxlength="6"
            />
            <el-button type="primary" :disabled="codeCountdown > 0" @click="handleGetCode">
              {{
                codeCountdown > 0
                  ? `${codeCountdown}${$t('login.registerForm.countdown')}`
                  : $t('login.registerForm.getCode')
              }}
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 英文模式下显示邮箱+密码注册 -->
      <template v-else>
        <el-form-item prop="email">
          <el-input
            v-model="registerForm.email"
            :placeholder="$t('login.registerForm.email.placeholder')"
            size="large"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="registerForm.password"
            :placeholder="$t('login.registerForm.password.placeholder')"
            size="large"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleRegister"
          />
        </el-form-item>
      </template>

      <el-form-item prop="name">
        <div class="name-gender-group">
          <el-input
            v-model="registerForm.name"
            :placeholder="$t('login.registerForm.name.placeholder')"
            size="large"
            prefix-icon="User"
            clearable
          />
          <el-radio-group v-model="registerForm.gender" size="large">
            <el-radio :label="$t('login.registerForm.gender.male')">{{
              $t('login.registerForm.gender.male')
            }}</el-radio>
            <el-radio :label="$t('login.registerForm.gender.female')">{{
              $t('login.registerForm.gender.female')
            }}</el-radio>
          </el-radio-group>
        </div>
      </el-form-item>

      <el-form-item prop="company">
        <el-input
          v-model="registerForm.company"
          :placeholder="$t('login.registerForm.company.placeholder')"
          size="large"
          prefix-icon="OfficeBuilding"
          clearable
        />
      </el-form-item>

      <el-form-item prop="role">
        <el-select
          v-model="registerForm.role"
          :placeholder="$t('login.registerForm.role.placeholder')"
          size="large"
          style="width: 100%"
        >
          <el-option
            :label="$t('login.registerForm.role.carSource')"
            :value="$t('login.registerForm.role.carSource')"
          />
          <el-option
            :label="$t('login.registerForm.role.trader')"
            :value="$t('login.registerForm.role.trader')"
          />
          <el-option
            :label="$t('login.registerForm.role.overseasBuyer')"
            :value="$t('login.registerForm.role.overseasBuyer')"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          size="large"
          class="submit-btn"
          :loading="registerLoading"
          @click="handleRegister"
        >
          {{
            registerLoading ? $t('login.registerForm.submitting') : $t('login.registerForm.submit')
          }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { InputInstance } from 'element-plus'
import { useSellwellautoApi } from '@/composables/useSellwellautoApi'
import { useLafApi } from '@/composables/useLafApi'
import { useI18nHelper } from '@/composables/useI18nHelper'

const registerFormRef = ref()
const registerLoading = ref(false)
const codeCountdown = ref(0)
const userStore = useUserStore()
const codeInputRef = ref<InputInstance>()
const { locale, t } = useI18nHelper() // 获取当前语言环境和翻译函数

const isZhCN = computed(() => locale.value === 'zh-CN')

// 注册表单数据
const registerForm = reactive({
  phone: '',
  code: '',
  name: '',
  gender: computed(() => t('login.registerForm.gender.male')),
  company: '',
  role: '',
  email: '',
  password: '',
})

// 注册表单验证规则
const registerRules = reactive({
  phone: [
    { required: true, message: t('login.registerForm.phone.required'), trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: t('login.registerForm.phone.invalid'), trigger: 'blur' },
  ],
  code: [
    { required: true, message: t('login.registerForm.code.required'), trigger: 'blur' },
    { len: 6, message: t('login.registerForm.code.length'), trigger: 'blur' },
  ],
  email: [
    { required: true, message: t('login.registerForm.email.required'), trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: t('login.registerForm.email.invalid'),
      trigger: 'blur',
    },
  ],
  password: [
    { required: true, message: t('login.registerForm.password.required'), trigger: 'blur' },
    { min: 6, message: t('login.registerForm.password.min'), trigger: 'blur' },
  ],
  name: [{ required: true, message: t('login.registerForm.name.required'), trigger: 'blur' }],
  company: [{ required: true, message: t('login.registerForm.company.required'), trigger: 'blur' }],
  role: [{ required: true, message: t('login.registerForm.role.required'), trigger: 'change' }],
})

// 获取 API 客户端
const { sendCode, login, authSigin } = useSellwellautoApi()
const { updateUserInfo, authSignUp } = useLafApi()

// 获取验证码
const handleGetCode = async () => {
  if (!isZhCN.value) return // 英文模式下不发送验证码
  if (!registerForm.phone) {
    ElMessage.warning(t('login.registerForm.phoneFirst'))
    return
  }

  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(registerForm.phone)) {
    ElMessage.warning(t('login.registerForm.invalidPhone'))
    return
  }

  try {
    await sendCode({
      action: 'auth:signIn',
      verifier: 'v_fxuxjiogmsm',
      uuid: registerForm.phone,
    })
    ElMessage.success(t('login.registerForm.codeSent'))
    codeInputRef.value?.focus()
    codeCountdown.value = 120
    const timer = setInterval(() => {
      codeCountdown.value--
      if (codeCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } catch (error: any) {
    ElMessage.error(error?.normalizedMessage || t('login.registerForm.codeFailed'))
  }
}

// 注册处理
const handleRegister = async () => {
  if (!registerFormRef.value) return
  await registerFormRef.value.validate()

  try {
    registerLoading.value = true
    let resp

    if (isZhCN.value) {
      // 手机号+验证码注册 (实际是登录，如果用户不存在则自动注册)
      resp = await login({
        uuid: registerForm.phone,
        code: registerForm.code,
      })
    } else {
      // 邮箱+密码注册
      await authSignUp({
        nickname: registerForm.name,
        username: registerForm.email,
        email: registerForm.email,
        phone: '',
        password: registerForm.password,
      })

      resp = await authSigin({
        account: registerForm.email,
        password: registerForm.password,
      })
    }

    if (resp && resp.data) {
      // 更新用户信息
      await updateUserInfo({
        id: resp.data.user.id,
        remark: `联系人：${registerForm.name || 'xxx'} ${registerForm.gender} \n企业名称：${registerForm.company} \n业务角色：${registerForm.role}`,
      })

      // 存储用户信息到store
      const userData = resp.data.user
      userStore.setUserInfo({
        id: userData.id,
        username: userData.username || userData.account || '',
        account: userData.account || userData.username || '',
        nickname: userData.nickname || userData.username || '',
        f_belong_company: userData.f_belong_company || '',
        token: resp.data.token,
        role: userData.role,
        email: userData.email,
        status: userData.status,
        createdAt: userData.createdAt,
        updatedAt: userData.updatedAt,
      })

      ElMessage.success(t('login.registerForm.success'))

      // 检查是否有保存的跳转路径
      let redirectPath = '/'
      if (import.meta.client) {
        redirectPath = sessionStorage.getItem('loginRedirect') || '/'
        if (redirectPath !== '/') {
          sessionStorage.removeItem('loginRedirect')
        }
      }

      // 跳转页面
      await navigateTo(redirectPath)

      // 清空表单
      Object.assign(registerForm, {
        phone: '',
        code: '',
        name: '',
        gender: t('login.registerForm.gender.male'),
        company: '',
        role: '',
        email: '',
        password: '',
      })
    } else {
      ElMessage.error(t('login.registerForm.failed'))
    }
  } catch (error: any) {
    ElMessage.error(error?.normalizedMessage || error.message || t('login.registerForm.failed'))
  } finally {
    registerLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
@use '../../assets/styles/variables' as *;

.form-content {
  animation: fadeIn 0.3s ease;
}

.form-header {
  text-align: center;
  margin-bottom: $spacing-xl;
}

.form-title {
  font-size: $font-size-extra-large;
  font-weight: bold;
  color: $text-primary;
  margin: 0 0 $spacing-xs 0;
}

.form-subtitle {
  font-size: $font-size-base;
  color: $text-secondary;
  margin: 0;
}

.register-form {
  margin-bottom: $spacing-lg;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(90deg, $primary-color, #ff8c42);
  border: none;
  height: 48px;
  font-size: $font-size-medium;
  font-weight: 500;

  &:hover {
    background: linear-gradient(90deg, #e55a2b, #e67e3a);
  }
}

.code-input-group {
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  .el-input {
    flex: 1;
  }

  .el-button {
    width: 120px;
    flex-shrink: 0;
  }
}

.name-gender-group {
  display: flex;
  gap: $spacing-sm;
  align-items: center;

  .el-input {
    flex: 1;
  }

  .el-radio-group {
    flex-shrink: 0;
    white-space: nowrap;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
