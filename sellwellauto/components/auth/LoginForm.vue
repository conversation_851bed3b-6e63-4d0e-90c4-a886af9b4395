<template>
  <div class="form-content">
    <div class="form-header">
      <h2 class="form-title">{{ $t('login.loginForm.title') }}</h2>
      <p class="form-subtitle">{{ $t('login.loginForm.subtitle') }}</p>
    </div>

    <el-form
      ref="loginFormRef"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
      @submit.prevent="handleLogin"
    >
      <!-- 中文模式下显示手机号+验证码登录 -->
      <template v-if="isZhCN">
        <el-form-item prop="phone">
          <el-input
            v-model="loginForm.phone"
            :placeholder="$t('login.loginForm.phone.placeholder')"
            size="large"
            prefix-icon="Phone"
            clearable
            maxlength="11"
          />
        </el-form-item>

        <el-form-item prop="code">
          <div class="code-input-group">
            <el-input
              ref="codeInputRef"
              v-model="loginForm.code"
              :placeholder="$t('login.loginForm.code.placeholder')"
              size="large"
              prefix-icon="Key"
              maxlength="6"
              @keyup.enter="handleLogin"
            />
            <el-button type="primary" :disabled="codeCountdown > 0" @click="handleGetCode">
              {{
                codeCountdown > 0
                  ? `${codeCountdown}${$t('login.loginForm.countdown')}`
                  : $t('login.loginForm.getCode')
              }}
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 英文模式下显示邮箱+密码登录 -->
      <template v-else>
        <el-form-item prop="email">
          <el-input
            v-model="loginForm.email"
            :placeholder="$t('login.loginForm.email.placeholder')"
            size="large"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            :placeholder="$t('login.loginForm.password.placeholder')"
            size="large"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
      </template>

      <el-form-item>
        <el-button
          type="primary"
          size="large"
          class="submit-btn"
          :loading="loading"
          @click="handleLogin"
        >
          {{ loading ? $t('login.loginForm.submitting') : $t('login.loginForm.submit') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { InputInstance } from 'element-plus'
import { useSellwellautoApi } from '@/composables/useSellwellautoApi'
import { useI18nHelper } from '@/composables/useI18nHelper'

const userStore = useUserStore()
const loginFormRef = ref()
const loading = ref(false)
const { locale, t } = useI18nHelper() // 获取当前语言环境和翻译函数

const isZhCN = computed(() => locale.value === 'zh-CN')

// 登录表单数据（手机号 + 验证码 或 邮箱 + 密码）
const loginForm = reactive({
  phone: '',
  code: '',
  email: '',
  password: '',
})

// 登录表单验证规则
const loginRules = reactive({
  phone: [
    { required: true, message: t('login.loginForm.phone.required'), trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: t('login.loginForm.phone.invalid'), trigger: 'blur' },
  ],
  code: [
    { required: true, message: t('login.loginForm.code.required'), trigger: 'blur' },
    { len: 6, message: t('login.loginForm.code.length'), trigger: 'blur' },
  ],
  email: [
    { required: true, message: t('login.loginForm.email.required'), trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: t('login.loginForm.email.invalid'),
      trigger: 'blur',
    },
  ],
  password: [
    { required: true, message: t('login.loginForm.password.required'), trigger: 'blur' },
    { min: 6, message: t('login.loginForm.password.min'), trigger: 'blur' },
  ],
})

const codeCountdown = ref(0)
const codeInputRef = ref<InputInstance>()

// 获取 API 客户端
const { sendCode, login, authSigin } = useSellwellautoApi()

// 发送验证码
const handleGetCode = async () => {
  if (!loginForm.phone) {
    ElMessage.warning($t('login.loginForm.phoneFirst'))
    return
  }
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(loginForm.phone)) {
    ElMessage.warning($t('login.loginForm.invalidPhone'))
    return
  }

  try {
    await sendCode({
      action: 'auth:signIn',
      verifier: 'v_fxuxjiogmsm',
      uuid: loginForm.phone,
    })
    ElMessage.success($t('login.loginForm.codeSent'))
    codeInputRef.value?.focus()
    codeCountdown.value = 120
    const timer = setInterval(() => {
      codeCountdown.value--
      if (codeCountdown.value <= 0) clearInterval(timer)
    }, 1000)
  } catch (error: any) {
    ElMessage.error(error?.normalizedMessage || $t('login.loginForm.codeFailed'))
  }
}

// 登录处理（未注册将自动注册）
const handleLogin = async () => {
  if (!loginFormRef.value) return
  await loginFormRef.value.validate()

  try {
    loading.value = true
    let response

    if (isZhCN.value) {
      // 手机号+验证码登录
      response = await login({
        uuid: loginForm.phone,
        code: loginForm.code,
      })
    } else {
      // 邮箱+密码登录
      response = await authSigin({
        account: loginForm.email,
        password: loginForm.password,
      })
    }

    if (response && response.data) {
      ElMessage.success(t('login.loginForm.success'))
      // 存储用户信息到store
      userStore.setUserInfo({
        ...response.data.user,
        token: response.data.token,
      })

      // 检查是否有保存的跳转路径
      let redirectPath = '/'
      if (import.meta.client) {
        redirectPath = sessionStorage.getItem('loginRedirect') || '/'
        if (redirectPath !== '/') {
          sessionStorage.removeItem('loginRedirect')
        }
      }

      // 跳转页面
      await navigateTo(redirectPath)
    } else {
      ElMessage.error(response?.data?.message || t('login.loginForm.failed'))
    }
  } catch (error: any) {
    ElMessage.error(error?.normalizedMessage || error.message || t('login.loginForm.failed'))
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
@use '../../assets/styles/variables' as *;

.form-content {
  animation: fadeIn 0.3s ease;
}

.form-header {
  text-align: center;
  margin-bottom: $spacing-xl;
}

.form-title {
  font-size: $font-size-extra-large;
  font-weight: bold;
  color: $text-primary;
  margin: 0 0 $spacing-xs 0;
}

.form-subtitle {
  font-size: $font-size-base;
  color: $text-secondary;
  margin: 0;
}

.login-form {
  margin-bottom: $spacing-lg;
}

.code-input-group {
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  .el-input {
    flex: 1;
  }

  .el-button {
    width: 120px;
    flex-shrink: 0;
  }
}

.submit-btn {
  width: 100%;
  background: linear-gradient(90deg, $primary-color, #ff8c42);
  border: none;
  height: 48px;
  font-size: $font-size-medium;
  font-weight: 500;

  &:hover {
    background: linear-gradient(90deg, #e55a2b, #e67e3a);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
