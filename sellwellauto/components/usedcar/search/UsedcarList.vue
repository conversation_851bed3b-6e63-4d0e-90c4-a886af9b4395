<template>
  <div class="usedcar-list">
    <div class="list-container">
      <div class="car-grid">
        <div
          v-for="(car, index) in carList"
          :key="car.id"
          class="car-card"
          :class="{ selected: selectedCars.includes(car.id) }"
          @click="handleCarClick($event, car.id)"
        >
          <!-- 选择器 -->
          <div v-if="carSelectionMode" class="selection-checkbox">
            <el-checkbox
              :model-value="selectedCars.includes(car.id)"
              @change="toggleSelection(car.id)"
            />
          </div>

          <!-- 车辆图片区域 -->
          <div class="car-image-container">
            <img
              :src="car.image"
              :alt="car.title"
              class="car-image"
              @error="handleImageError"
            />

            <!-- 价格标签 -->
            <div class="price-tag">${{ formatPrice(car.price) }}</div>
          </div>

          <!-- 车辆信息区域 -->
          <div class="car-info">
            <!-- 车辆标题 -->
            <h3 class="car-title">{{ car.title }} -{{ index }}</h3>

            <!-- 位置信息 -->
            <div class="location">
              <el-icon class="location-icon">
                <Location />
              </el-icon>
              <div v-if="car.description" class="car-description">
                {{ car.description }}
              </div>
            </div>

            <!-- 车辆规格 -->
            <div class="car-specs">
              <div
                v-for="(row, rowIndex) in getCarSpecs(car)"
                :key="rowIndex"
                class="spec-row"
              >
                <div
                  v-for="(item, itemIndex) in row"
                  :key="itemIndex"
                  class="spec-item"
                >
                  <span class="spec-label">{{ item.label }}:</span>
                  <span class="spec-value">{{ item.value }}</span>
                </div>
              </div>
            </div>

            <!-- 配置亮点标签 -->
            <div v-if="car.config_highlights" class="config-tags">
              <span
                v-for="(tag, tagIndex) in car.config_highlights"
                :key="tagIndex"
                class="config-tag"
              >
                {{ tag }}
              </span>
            </div>

            <!-- 过户次数标签 -->
            <div v-if="car.tags && car.tags.length > 0" class="transfer-tags">
              <span
                v-for="(tag, tagIndex) in car.tags"
                :key="tagIndex"
                class="transfer-tag"
              >
                {{ tag }}
              </span>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="car-actions" @click.stop>
            <el-button class="action-btn inquire-btn" :icon="ChatDotRound">
              询价
            </el-button>

            <el-button
              class="action-btn detail-btn"
              type="primary"
              :icon="Position"
            >
              查看详情
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Location, ChatDotRound, Position } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { ref, watch } from 'vue'

// 定义props
interface Props {
  carList: any[]
  carSelectionMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  carList: () => [],
  carSelectionMode: false
})

// 定义emits
const emit = defineEmits<{
  'update:selectedCars': [selectedCars: number[]]
}>()

// 选中的车辆ID数组
const selectedCars = ref<number[]>([])

// 监听选车模式变化，重置选中状态
watch(
  () => props.carSelectionMode,
  (newMode) => {
    if (!newMode) {
      selectedCars.value = []
    }
  }
)

// 处理车辆卡片点击
const handleCarClick = (event: Event, carId: number) => {
  // 如果点击的是选择器区域，不处理
  const target = event.target as HTMLElement
  if (target.closest('.selection-checkbox')) {
    return
  }

  if (props.carSelectionMode) {
    toggleSelection(carId)
  }
}

// 切换选中状态
const toggleSelection = (carId: number) => {
  if (!props.carSelectionMode) return

  const index = selectedCars.value.indexOf(carId)
  if (index > -1) {
    selectedCars.value.splice(index, 1)
  } else {
    selectedCars.value.push(carId)
  }

  // 向父组件发送选中的车辆ID数组
  emit('update:selectedCars', [...selectedCars.value])
}

// 格式化价格
const formatPrice = (price: number): string => {
  return (price * 10000).toLocaleString()
}

// 格式化日期
const formatDate = (dateString: string): string => {
  return dayjs(dateString).format('YYYY-MM-DD')
}

// 获取车辆规格数据
const getCarSpecs = (car: any) => {
  return [
    [
      {
        label: '发布时间',
        value: formatDate(car.publish_date)
      },
      {
        label: '注册年份',
        value: formatDate(car.first_license_date)
      }
    ],
    [
      {
        label: '里程',
        value: `${car.mileage}万公里`
      },
      {
        label: '过户次数',
        value: car.transfer_count ? `${car.transfer_count}次` : '0次'
      }
    ],
    [
      {
        label: '驱动',
        value: car.model?.drive || '未知'
      },
      {
        label: '燃料',
        value: car.model?.energy_type_up || '未知'
      }
    ],
    [
      {
        label: '变速箱',
        value: car.model?.gearbox || '未知'
      },
      {
        label: '座位数',
        value: car.model?.seats ? `${car.model.seats}座` : '未知'
      }
    ]
  ]
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '' // 设置默认图片
}
</script>

<style lang="scss" scoped>
.usedcar-list {
  padding: 24px 24px 100px 24px;

  .list-container {
    max-width: $max-width;
    margin: 0 auto;
  }

  .car-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    align-items: start;

    @media (max-width: 992px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    .car-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition:
        transform 0.3s ease,
        box-shadow 0.3s ease,
        border-color 0.3s ease;
      height: fit-content;
      position: relative;
      cursor: pointer;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      &.selected {
        border: 2px solid #ff6b35;
        box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4);
        }
      }
    }
  }

  .selection-checkbox {
    position: absolute;
    top: 12px;
    left: 12px;
    z-index: 10;

    :deep(.el-checkbox) {
      .el-checkbox__input {
        .el-checkbox__inner {
          width: 20px;
          height: 20px;
          border-width: 2px;
        }
      }
    }
  }

  .car-image-container {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;

    .car-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    .price-tag {
      position: absolute;
      bottom: 12px;
      right: 12px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .car-info {
    padding: 16px;

    .car-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .location {
      display: flex;
      align-items: flex-start;
      gap: 4px;
      margin-bottom: 12px;
      color: #666;
      font-size: 14px;

      .location-icon {
        color: #f56c6c;
        font-size: 14px;
        margin-top: 2px;
        flex-shrink: 0;
      }

      .car-description {
        color: #666;
        font-size: 13px;
        line-height: 1.4;
        margin-top: 0;
      }
    }

    .car-specs {
      margin-bottom: 12px;

      .spec-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 6px;

        .spec-item {
          flex: 1;
          font-size: 13px;

          .spec-label {
            color: #666;
            margin-right: 4px;
          }

          .spec-value {
            color: #333;
            font-weight: 500;
          }
        }
      }
    }

    .config-tags {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3; // 限制显示3行
      overflow: hidden;
      text-overflow: ellipsis;
      gap: 6px;
      margin-bottom: 8px;
      line-height: 1.5;

      .config-tag {
        background-color: #f0f0f0;
        color: #666;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        display: inline-block;
        margin-right: 6px;
        margin-bottom: 6px;
      }
    }

    .transfer-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .transfer-tag {
        background-color: #e8f4fd;
        color: #1890ff;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
      }
    }
  }

  .car-actions {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 8px;

    .action-btn {
      flex: 1;
      height: 36px;
      font-size: 13px;
      border-radius: 6px;
      border: 1px solid #dcdfe6;

      &.inquire-btn {
        background: white;
        color: #333;

        &:hover {
          background-color: #f5f5f5;
        }
      }

      &.favorite-btn {
        background: white;
        color: #333;

        &:hover {
          background-color: #f5f5f5;
        }
      }

      &.detail-btn {
        background-color: #ff6b35;
        border-color: #ff6b35;
        color: white;

        &:hover {
          background-color: #e55a2b;
          border-color: #e55a2b;
        }
      }
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-button) {
  font-weight: 500;
}

:deep(.el-icon) {
  margin-right: 4px;
}
</style>
