<template>
  <div class="advanced-search">
    <!-- 智能找车 -->
    <div
      class="search-ai-analyse"
      :class="{ expanded: isExpanded }"
      @mouseenter="expandSearch"
      @mouseleave="collapseSearch"
    >
      <div v-show="!isExpanded" class="collapsed-content">
        <div class="content" :class="{ 'no-content': aiSearchText == '' }">
          {{ aiSearchText || $t('advancedSearch.smartSearch.placeholder') }}
        </div>
      </div>

      <!-- 展开状态：显示输入框和搜索按钮 -->
      <div v-show="isExpanded" class="expanded-content">
        <div class="search-form">
          <el-input
            ref="smartSearchInput"
            v-model="aiSearchText"
            type="textarea"
            :rows="2"
            :placeholder="$t('advancedSearch.smartSearch.placeholder')"
            class="form-input"
            @focus="expandSearch"
          />
          <el-button
            type="primary"
            class="search-ai-button"
            :icon="Search"
            :disabled="!aiSearchText"
            :loading="aiSearchLoading"
            @click="handleParseDemand"
          >
            {{ $t('advancedSearch.smartSearch.button') }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 手动选车 -->
    <div class="search-manual">
      <div class="title">
        <div class="title-bar" />
        <h2>{{ $t('advancedSearch.title') }}</h2>
      </div>

      <div class="group">
        <div class="filters">
          <div class="filter-row">
            <div class="filter-item">
              <label>{{
                $t('advancedSearch.manualSearch.filters.brand')
              }}</label>
              <el-select
                v-model="searchForm.brand"
                :placeholder="$t('common.all')"
                clearable
                :loading="pending"
              >
                <el-option
                  v-for="brand in brandOptions"
                  :key="brand.value"
                  :label="brand.label"
                  :value="brand.value"
                />
              </el-select>
            </div>
            <div class="filter-item">
              <label>{{
                $t('advancedSearch.manualSearch.filters.series')
              }}</label>
              <el-select
                v-model="searchForm.series"
                :placeholder="$t('common.all')"
                clearable
                :disabled="!searchForm.brand"
              >
                <el-option
                  v-for="model in modelOptions"
                  :key="model.value"
                  :label="model.label"
                  :value="model.value"
                />
              </el-select>
            </div>
            <div class="filter-item">
              <label>{{ $t('advancedSearch.manualSearch.filters.age') }}</label>
              <el-select
                v-model="searchForm.age"
                :placeholder="$t('common.all')"
                clearable
              >
                <el-option
                  v-for="age in ageOptions"
                  :key="age.value"
                  :label="age.label"
                  :value="age.value"
                />
              </el-select>
            </div>
          </div>

          <div class="filter-row">
            <div class="filter-item">
              <label>{{
                $t('advancedSearch.manualSearch.filters.transmission')
              }}</label>
              <el-select
                v-model="searchForm.transmission"
                :placeholder="$t('common.all')"
                clearable
              >
                <el-option
                  v-for="transmission in transmissionOptions"
                  :key="transmission.value"
                  :label="transmission.label"
                  :value="transmission.value"
                />
              </el-select>
            </div>

            <div class="filter-item">
              <label>{{
                $t('advancedSearch.manualSearch.filters.seats')
              }}</label>
              <el-select
                v-model="searchForm.seats"
                :placeholder="$t('common.all')"
                clearable
              >
                <el-option
                  v-for="seat in seatsOptions"
                  :key="seat.value"
                  :label="seat.label"
                  :value="seat.value"
                />
              </el-select>
            </div>
            <div class="filter-item">
              <label>{{
                $t('advancedSearch.manualSearch.filters.energy')
              }}</label>
              <el-select
                v-model="searchForm.energy"
                :placeholder="$t('common.all')"
                clearable
              >
                <el-option
                  v-for="energy in energyOptions"
                  :key="energy.value"
                  :label="energy.label"
                  :value="energy.value"
                />
              </el-select>
            </div>

            <div class="filter-item">
              <label>{{
                $t('advancedSearch.manualSearch.filters.emission')
              }}</label>
              <el-select
                v-model="searchForm.emission"
                :placeholder="$t('common.all')"
                clearable
              >
                <el-option
                  v-for="emission in emissionOptions"
                  :key="emission.value"
                  :label="emission.label"
                  :value="emission.value"
                />
              </el-select>
            </div>
          </div>
        </div>
      </div>

      <div class="group">
        <div class="filters">
          <div class="slider-section">
            <label>{{
              $t('advancedSearch.manualSearch.filters.mileage')
            }}</label>
            <div class="slider-container">
              <span class="slider-value slider-value-left">{{
                searchForm.mileage[0]
              }}</span>
              <el-slider
                v-model="searchForm.mileage"
                range
                :min="0"
                :max="50"
                :step="1"
                :format-tooltip="(val) => val.toLocaleString()"
              />
              <span class="slider-value">{{ searchForm.mileage[1] }}</span>
            </div>
          </div>

          <div class="slider-section">
            <label>{{ $t('advancedSearch.manualSearch.filters.price') }}</label>
            <div class="slider-container">
              <span class="slider-value slider-value-left">{{
                searchForm.price[0]
              }}</span>
              <el-slider
                v-model="searchForm.price"
                range
                :min="0"
                :max="800"
                :step="1"
                :format-tooltip="(val) => val.toLocaleString()"
              />
              <span class="slider-value">{{ searchForm.price[1] }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="color-section-full">
        <label>{{ $t('advancedSearch.manualSearch.filters.color') }}</label>
        <div class="color-palette">
          <div
            v-for="color in colors"
            :key="color.value"
            :class="[
              'color-swatch',
              { active: searchForm.color === color.value }
            ]"
            :style="{ backgroundColor: color.hex }"
            @click="searchForm.color = color.value"
          />
        </div>
      </div>

      <div class="search-manual-button">
        <el-button
          type="primary"
          :icon="Search"
          :loading="searchUsedCarLoading"
          @click="handleSearchUsedCar"
          >{{ $t('advancedSearch.manualSearch.button') }}</el-button
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'

const { parseDemand, carTree, searchUsedCar, createCarSearchTask } =
  useSellwellautoServerApi()
const { locale } = useI18n()
const searchStore = useSearchStore()

// 定义 emit 事件
const emit = defineEmits<{
  'search-result': [data: { cars: any[]; total: number }]
  'search-error': [error: string]
}>()

// 在服务端渲染时获取品牌数据
const {
  data: carTreeData,
  pending,
  refresh: refreshCarTree
} = await useLazyAsyncData(
  'carTree',
  async () => {
    try {
      return await carTree()
    } catch (err) {
      console.error('获取品牌车系数据失败:', err)
      return null
    }
  },
  {
    server: true, // 确保在服务端执行
    default: () => null
  }
)

// 监听语言变化，重新获取品牌数据
watch(
  () => locale.value,
  async (newLocale, oldLocale) => {
    if (newLocale !== oldLocale) {
      await refreshCarTree()
    }
  },
  { immediate: false }
)

// 搜索表单数据
const searchForm = reactive({
  brand: null as number | null,
  series: null as number | null,
  transmission: '',
  seats: '',
  energy: '',
  emission: '',
  mileage: [0, 50] as [number, number],
  price: [0, 800] as [number, number],
  age: null as number | null,
  color: ''
})

// 初始化状态标记，防止初始化过程中触发清空逻辑
const isInitializing = ref(false)

// 初始化搜索表单
const initializeSearchForm = async () => {
  // 确保品牌数据已加载
  if (!carTreeData.value) {
    return
  }

  // 优先使用状态管理中的搜索条件
  const condition = searchStore.getSearchCondition()

  // 如果有状态管理中的条件，使用它
  if (searchStore.isFromHomeSearch && Object.keys(condition).length > 0) {
    isInitializing.value = true

    // 如果有搜索文本，优先处理智能搜索
    if (condition.search_text) {
      aiSearchText.value = condition.search_text
      isExpanded.value = true // 自动展开搜索区域

      try {
        // 解析需求
        aiSearchLoading.value = true
        const res: any = await parseDemand(condition.search_text)
        const {
          brand_id,
          series_id,
          color,
          max_mileage,
          max_price,
          vehicle_age
        } = res.data

        // 更新搜索表单
        searchForm.brand = Number(brand_id) || null
        searchForm.mileage = [0, Number(max_mileage) || 50]
        searchForm.price = [0, Number(max_price) || 800]
        searchForm.age = Number(vehicle_age) || null
        searchForm.color = color || ''

        // 等待品牌选择更新后再设置车系
        nextTick(() => {
          searchForm.series = Number(series_id) || null
          // 更新上一次搜索的信息
          lastSearchInfo.value = {
            brand: searchForm.brand,
            series: searchForm.series
          }
          isInitializing.value = false
        })
      } catch (error) {
        console.error('解析智能搜索需求失败:', error)
        isInitializing.value = false
      } finally {
        aiSearchLoading.value = false
      }
    } else {
      // 如果没有搜索文本，使用普通条件
      searchForm.brand =
        condition.brand_id != null ? Number(condition.brand_id) : null
      nextTick(() => {
        searchForm.series =
          condition.series_id != null ? Number(condition.series_id) : null
        // 更新上一次搜索的信息
        lastSearchInfo.value = {
          brand: searchForm.brand,
          series: searchForm.series
        }
        isInitializing.value = false

        handleSearchUsedCar()
      })
    }

    // 清除状态管理中的条件，避免重复使用
    searchStore.clearSearchCondition()
  }
}

// AI搜索文本
const aiSearchText = ref('')
const aiSearchLoading = ref(false)
const searchUsedCarLoading = ref(false)

// 控制搜索区域展开状态
const isExpanded = ref(false)

// 展开搜索区域
const expandSearch = () => {
  isExpanded.value = true
  // 展开后自动获取焦点
  nextTick(() => {
    smartSearchInput.value?.focus()
  })
}

// 收缩搜索区域
const collapseSearch = () => {
  if (aiSearchLoading.value) return
  isExpanded.value = false
}

// 输入框
const smartSearchInput = ref()

// 车龄选项
// 获取i18n实例
const { t } = useI18n()

const ageOptions = [
  {
    label: t('advancedSearch.manualSearch.ageOptions.withinYear', 1),
    value: 1
  },
  {
    label: t('advancedSearch.manualSearch.ageOptions.withinYear', 2),
    value: 2
  },
  {
    label: t('advancedSearch.manualSearch.ageOptions.withinYear', 3),
    value: 3
  },
  {
    label: t('advancedSearch.manualSearch.ageOptions.withinYear', 5),
    value: 5
  },
  {
    label: t('advancedSearch.manualSearch.ageOptions.withinYear', 7),
    value: 7
  },
  { label: t('common.unlimited'), value: 10 }
]

// 变速箱选项
const transmissionOptions = [
  {
    label: t('advancedSearch.manualSearch.transmissionOptions.manual'),
    value: '手动'
  },
  {
    label: t('advancedSearch.manualSearch.transmissionOptions.automatic'),
    value: '自动'
  },
  {
    label: t('advancedSearch.manualSearch.transmissionOptions.manualAutomatic'),
    value: '手自一体'
  },
  {
    label: t('advancedSearch.manualSearch.transmissionOptions.cvt'),
    value: '无级变速'
  },
  {
    label: t('advancedSearch.manualSearch.transmissionOptions.dualClutch'),
    value: '双离合'
  }
]

// 座位数选项
const seatsOptions = [
  {
    label: t('advancedSearch.manualSearch.seatsOptions.seats', 2),
    value: '2座'
  },
  {
    label: t('advancedSearch.manualSearch.seatsOptions.seats', 4),
    value: '4座'
  },
  {
    label: t('advancedSearch.manualSearch.seatsOptions.seats', 5),
    value: '5座'
  },
  {
    label: t('advancedSearch.manualSearch.seatsOptions.seats', 6),
    value: '6座'
  },
  {
    label: t('advancedSearch.manualSearch.seatsOptions.seats', 7),
    value: '7座'
  },
  {
    label: t('advancedSearch.manualSearch.seatsOptions.seats', 8),
    value: '8座'
  },
  {
    label: t('advancedSearch.manualSearch.seatsOptions.seatsAbove', 8),
    value: '8座以上'
  }
]

// 能源选项
const energyOptions = [
  {
    label: t('advancedSearch.manualSearch.energyOptions.rangeExtender'),
    value: '增程式'
  },
  {
    label: t('advancedSearch.manualSearch.energyOptions.pluginHybrid'),
    value: '插电式混合动力'
  },
  {
    label: t('advancedSearch.manualSearch.energyOptions.phev'),
    value: '插电混动'
  },
  {
    label: t('advancedSearch.manualSearch.energyOptions.newEnergy'),
    value: '新能源'
  },
  {
    label: t('advancedSearch.manualSearch.energyOptions.diesel'),
    value: '柴油'
  },
  {
    label: t('advancedSearch.manualSearch.energyOptions.gasoline'),
    value: '汽油'
  },
  {
    label: t('advancedSearch.manualSearch.energyOptions.gasolineMildHybrid'),
    value: '汽油+48V轻混系统'
  },
  {
    label: t('advancedSearch.manualSearch.energyOptions.gasLpgHybrid'),
    value: '油气混动'
  },
  {
    label: t('advancedSearch.manualSearch.energyOptions.gasLpgMix'),
    value: '油气混合'
  },
  {
    label: t('advancedSearch.manualSearch.energyOptions.gasolineHybrid'),
    value: '油电混动'
  },
  {
    label: t('advancedSearch.manualSearch.energyOptions.gasolineElectricMix'),
    value: '油电混合'
  },
  { label: t('advancedSearch.manualSearch.energyOptions.lpg'), value: '燃气' },
  {
    label: t('advancedSearch.manualSearch.energyOptions.bev'),
    value: '纯电动'
  }
]

// 排放标准选项
const emissionOptions = [
  {
    label: t('advancedSearch.manualSearch.emissionOptions.chinaI'),
    value: '国I'
  },
  {
    label: t('advancedSearch.manualSearch.emissionOptions.chinaII'),
    value: '国II'
  },
  {
    label: t('advancedSearch.manualSearch.emissionOptions.chinaIII'),
    value: '国III'
  },
  {
    label: t('advancedSearch.manualSearch.emissionOptions.chinaIIIOBD'),
    value: '国III+OBD'
  },
  {
    label: t('advancedSearch.manualSearch.emissionOptions.chinaIV'),
    value: '国IV'
  },
  {
    label: t('advancedSearch.manualSearch.emissionOptions.chinaV'),
    value: '国V'
  },
  {
    label: t('advancedSearch.manualSearch.emissionOptions.chinaVI'),
    value: '国VI'
  },
  {
    label: t('advancedSearch.manualSearch.emissionOptions.chinaVIb'),
    value: '国VIb'
  },
  {
    label: t('advancedSearch.manualSearch.emissionOptions.euroI'),
    value: '欧I'
  },
  {
    label: t('advancedSearch.manualSearch.emissionOptions.euroII'),
    value: '欧II'
  },
  {
    label: t('advancedSearch.manualSearch.emissionOptions.euroIV'),
    value: '欧IV'
  },
  {
    label: t('advancedSearch.manualSearch.emissionOptions.euroV'),
    value: '欧V'
  },
  {
    label: t('advancedSearch.manualSearch.emissionOptions.euroVI'),
    value: '欧VI'
  }
]

// 颜色选项
const colors = [
  {
    value: '黑色',
    hex: '#000000',
    name: t('advancedSearch.manualSearch.colorOptions.black')
  },
  {
    value: '白色',
    hex: '#FFFFFF',
    name: t('advancedSearch.manualSearch.colorOptions.white')
  },
  {
    value: '红色',
    hex: '#FF0000',
    name: t('advancedSearch.manualSearch.colorOptions.red')
  },
  {
    value: '绿色',
    hex: '#008000',
    name: t('advancedSearch.manualSearch.colorOptions.green')
  },
  {
    value: '蓝色',
    hex: '#0066CC',
    name: t('advancedSearch.manualSearch.colorOptions.blue')
  },
  {
    value: '黄色',
    hex: '#FFFF00',
    name: t('advancedSearch.manualSearch.colorOptions.yellow')
  },
  {
    value: '棕色',
    hex: '#8B4513',
    name: t('advancedSearch.manualSearch.colorOptions.brown')
  },
  {
    value: '米色',
    hex: '#F5F5DC',
    name: t('advancedSearch.manualSearch.colorOptions.beige')
  },
  {
    value: '灰色',
    hex: '#808080',
    name: t('advancedSearch.manualSearch.colorOptions.gray')
  },
  {
    value: '橙色',
    hex: '#FFA500',
    name: t('advancedSearch.manualSearch.colorOptions.orange')
  },
  {
    value: '深灰色',
    hex: '#2F2F2F',
    name: t('advancedSearch.manualSearch.colorOptions.darkGray')
  },
  {
    value: '粉色',
    hex: '#FFC0CB',
    name: t('advancedSearch.manualSearch.colorOptions.pink')
  },
  {
    value: '紫色',
    hex: '#800080',
    name: t('advancedSearch.manualSearch.colorOptions.purple')
  },
  {
    value: '浅灰色',
    hex: '#F8F8FF',
    name: t('advancedSearch.manualSearch.colorOptions.lightGray')
  }
]

// 处理品牌数据，提取品牌选项
const brandOptions = computed(() => {
  if (!carTreeData.value) return []

  // 根据实际返回的数据结构处理品牌数据
  try {
    const data = carTreeData.value as any
    const tree = data?.data?.tree || []

    return tree.map((brand: any) => ({
      label: brand.name,
      value: brand.id as number
    }))
  } catch (error) {
    console.error('处理品牌数据时出错:', error)
    return []
  }
})

// 处理车型数据
const modelOptions = computed(() => {
  if (!carTreeData.value || !searchForm.brand) return []

  try {
    const data = carTreeData.value as any
    const tree = data?.data?.tree || []
    const selectedBrand = tree.find(
      (brand: any) => brand.id === searchForm.brand
    )

    if (!selectedBrand || !selectedBrand.children) return []

    return selectedBrand.children.map((series: any) => ({
      label: series.name,
      value: series.id
    }))
  } catch (error) {
    console.error('处理车型数据时出错:', error)
    return []
  }
})

// 监听品牌选择变化，清空车型选择
watch(
  () => searchForm.brand,
  () => {
    if (isInitializing?.value) return
    searchForm.series = null
  }
)

// 组件挂载时初始化搜索表单
onMounted(() => {
  // 使用 nextTick 确保 DOM 更新完成后再初始化
  nextTick(() => {
    initializeSearchForm()
  })
})

// 监听品牌数据加载完成
watch(
  () => carTreeData.value,
  (newData) => {
    if (newData) {
      nextTick(() => {
        initializeSearchForm()
      })
    }
  },
  { immediate: true }
)

// 监听状态管理变化，延迟初始化
watch(
  () => searchStore.isFromHomeSearch,
  (isFromHome) => {
    if (isFromHome) {
      // 延迟初始化，确保状态管理数据已更新
      nextTick(() => {
        initializeSearchForm()
      })
    }
  },
  { immediate: true }
)

// 解析需求
const handleParseDemand = async () => {
  if (!aiSearchText.value) {
    ElMessage.warning(useI18n().t('advancedSearch.messages.pleaseEnterDemand'))
    return
  }

  try {
    aiSearchLoading.value = true
    const res: any = await parseDemand(aiSearchText.value)
    const { brand_id, series_id, color, max_mileage, max_price, vehicle_age } =
      res.data

    const newSearchForm = {
      brand: Number(brand_id) || null,
      mileage: Number(max_mileage) || null,
      price: Number(max_price) || null,
      age: Number(vehicle_age) || null,
      color: color
    }
    Object.assign(searchForm, newSearchForm)
    nextTick(() => {
      Object.assign(searchForm, { model: Number(series_id) || null })
    })
    ElMessage.success(useI18n().t('advancedSearch.messages.parseSuccess'))
  } catch (error) {
    console.error('解析需求失败:', error)
    ElMessage.error(useI18n().t('advancedSearch.messages.parseFailed'))
  } finally {
    aiSearchLoading.value = false
  }
}

// 存储上一次搜索的品牌和车系信息
const lastSearchInfo = ref({
  brand: null as number | null,
  series: null as number | null
})

// 搜索
const handleSearchUsedCar = async () => {
  if (!searchForm.brand)
    return ElMessage.warning(useI18n().t('advancedSearch.messages.selectBrand'))

  try {
    searchUsedCarLoading.value = true
    const { brand, series, color, age, mileage, price } = searchForm

    // 检查品牌和车系是否发生变化
    const brandChanged = brand !== lastSearchInfo.value.brand
    const seriesChanged = series !== lastSearchInfo.value.series

    // 只有当品牌或车系发生变化时，才创建搜索任务
    if (brandChanged || seriesChanged) {
      await createCarSearchTask({
        search_text: aiSearchText.value,
        brand_id: brand || undefined,
        series_id: series || undefined
      })

      // 更新上一次搜索的信息
      lastSearchInfo.value = {
        brand,
        series
      }
    }

    // 搜索车源
    const searchBody = {
      search_text: aiSearchText.value || undefined,
      brand_id: brand || undefined,
      series_id: series || undefined,
      color: color || undefined,
      vehicle_age: age || undefined,
      min_mileage: mileage[0] || 0,
      max_mileage: mileage[1] || undefined,
      min_price: price[0] || 0,
      max_price: price[1] || undefined
    }

    const res: any = await searchUsedCar(searchBody)

    // 处理搜索结果
    if (res && res.data) {
      const cars = res.data.cars || res.data || []
      const total = res.data.total || res.total || cars.length

      // 向父组件发送搜索结果
      emit('search-result', { cars, total })
    } else {
      // 如果没有数据，发送空结果
      emit('search-result', { cars: [], total: 0 })
    }
  } catch (error: any) {
    console.error('搜索失败:', error)
    // 向父组件发送错误信息
    emit('search-error', error.message || '搜索失败，请稍后重试')
  } finally {
    searchUsedCarLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.advanced-search {
  padding: 24px;

  .title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    .title-bar {
      width: 4px;
      height: 24px;
      background-color: #ff6b35;
      border-radius: 2px;
    }

    h2 {
      margin: 0;
      color: #333;
      font-size: 20px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  .search-ai-analyse {
    width: 90%;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 12px;
    padding: 20px 20px 40px 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 60px; // 最小高度，被遮挡一半
    overflow: hidden; // 隐藏超出部分
    transition: min-height 0.3s ease-in-out; // 高度变化动画
    cursor: pointer; // 添加鼠标指针样式
    position: relative; // 为绝对定位的内容提供参考

    // 展开状态
    &.expanded {
      min-height: 120px; // 展开时设置固定高度
      overflow: visible; // 显示所有内容

      .collapsed-content {
        opacity: 0;
      }

      .expanded-content {
        opacity: 1;
        height: auto; // 自动高度
        display: flex;
        align-items: flex-start; // 改为顶部对齐
      }
    }

    // 收缩状态的内容
    .collapsed-content {
      opacity: 1;
      transition: opacity 0.3s ease-in-out;
      position: absolute;
      top: 20px;
      left: 20px;
      right: 20px;

      .content {
        color: #666;
        font-size: 14px;
      }

      .no-content {
        color: #999;
      }
    }

    // 展开状态的内容
    .expanded-content {
      opacity: 0;
      transition: opacity 0.3s ease-in-out;
      position: absolute;
      top: 20px;
      left: 20px;
      right: 20px;

      .search-form {
        width: 100%;
        position: relative;

        .form-input {
          width: 100%;
          /* 针对 textarea 组件去除边框样式 */
          :deep(.el-textarea__inner) {
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
          }

          :deep(.el-textarea__inner) {
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 0px 120px 30px 0px; // 右下角留出按钮空间
            font-size: 14px;
            resize: none;
            min-height: 60px; // 设置最小高度
            // 隐藏滚动条
            scrollbar-width: none; // Firefox
            -ms-overflow-style: none; // IE/Edge
            &::-webkit-scrollbar {
              display: none; // Chrome/Safari/Opera
            }
          }
        }

        .search-ai-button {
          position: absolute;
          bottom: 0;
          right: 22px;
          z-index: 10;
        }
      }
    }
  }

  .search-manual {
    background-color: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-top: -10px; // 向上移动，遮挡 search-ai-analyse 的一半
    position: relative; // 确保层级正确
    z-index: 1; // 确保在 search-ai-analyse 之上

    .group {
      display: flex;
      gap: 32px;
    }
  }

  .filters {
    flex: 1;
    min-width: 0;

    .filter-row {
      display: flex;
      gap: 16px;
      margin-bottom: 20px;

      .filter-item {
        flex: 1;
        min-width: 0;

        label {
          display: block;
          margin-bottom: 8px;
          color: #333;
          font-weight: 500;
          font-size: 14px;
        }

        .year-range {
          display: flex;
          align-items: center;
          gap: 8px;

          .range-separator {
            color: #666;
            font-weight: 500;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .slider-section {
    width: 100%;
    label {
      color: #333;
      font-weight: 500;
      font-size: 14px;
    }

    .slider-container {
      display: flex !important;
      align-items: center;
      .slider-value {
        margin-left: 12px;
        font-size: 14px;
        color: #666;
        font-weight: 700;
      }
      .slider-value-left {
        margin-left: 0;
        margin-right: 12px;
      }
    }
  }

  .color-section-full {
    label {
      display: block;
      margin-bottom: 16px;
      color: #333;
      font-weight: 500;
      font-size: 14px;
    }

    .color-palette {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(32px, 1fr));
      gap: 8px;
      /* 让颜色选择器的色块在小屏幕下自动换行，保证自适应 */

      .color-swatch {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 4px;

        // 创建多层边框效果
        &::before {
          content: '';
          position: absolute;
          top: -4px;
          left: -4px;
          right: -4px;
          bottom: -4px;
          background: #f0f0f0;
          border-radius: 8px;
          z-index: -2;
        }

        &::after {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          background: #ffffff;
          border-radius: 6px;
          z-index: -1;
        }

        &.active {
          border-color: $primary-color;
          box-shadow: 0 0 0 2px $primary-color;
        }
      }

      // 响应式设计 - 在不同屏幕尺寸下调整颜色块布局
      @media (max-width: 768px) {
        grid-template-columns: repeat(auto-fit, minmax(28px, 1fr));
        gap: 6px;

        .color-swatch {
          width: 20px;
          height: 20px;
          border-radius: 3px;
          margin: 3px;

          &::before {
            border-radius: 7px;
          }

          &::after {
            border-radius: 5px;
          }
        }
      }

      @media (max-width: 480px) {
        grid-template-columns: repeat(auto-fit, minmax(24px, 1fr));
        gap: 4px;

        .color-swatch {
          width: 18px;
          height: 18px;
          border-radius: 3px;
          margin: 2px;

          &::before {
            border-radius: 6px;
          }

          &::after {
            border-radius: 4px;
          }
        }
      }
    }
  }

  .search-manual-button {
    border-top: 1px solid #e0e0e0;
    padding-top: 20px;
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 12px;
  }
}

// Element Plus 样式覆盖
:deep(.el-select) {
  width: 100%;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
  border: 1px solid #dcdfe6;

  &:hover {
    border-color: #c0c4cc;
  }

  &.is-focus {
    border-color: $primary-color;
  }
}

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.el-slider__runway) {
  background-color: #e0e0e0;
  height: 6px;
  border-radius: 3px;
}

:deep(.el-slider__bar) {
  background-color: $primary-color;
  height: 6px;
  border-radius: 3px;
}

:deep(.el-slider__button) {
  background-color: $primary-color;
  border-color: $primary-color;
  width: 16px;
  height: 16px;
}

:deep(.el-slider__input) {
  width: 80px;
  margin-left: 12px;
}

:deep(.el-input__inner) {
  font-size: 12px;
  text-align: center;
}
</style>
