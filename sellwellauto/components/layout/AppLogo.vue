<template>
  <div class="app-logo">
    <NuxtLink to="/" class="logo-link" :title="logoTitle" :aria-label="logoAriaLabel">
      <img
        src="/logo.webp"
        :alt="logoAlt"
        :title="logoTitle"
        class="logo-image"
        loading="eager"
        decoding="async"
      />
      <!-- 添加隐藏的文本用于SEO -->
      <span class="sr-only">{{ logoText }}</span>
    </NuxtLink>
  </div>
</template>

<script setup lang="ts">
// Logo组件SEO优化
const logoTitle = '赛沃车联 Sellwell tech - 二手车出口高效对接平台'
const logoAlt = '赛沃车联 Sellwell tech Logo - 二手车出口平台标志'
const logoAriaLabel = '赛沃车联官方网站首页'
const logoText = '赛沃车联 Sellwell tech'

const config = useRuntimeConfig()

// 添加结构化数据
const structuredData = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: '赛沃车联',
  alternateName: 'Sellwell tech',
  url: config.public.sellwellautoUrl,
  logo: `${config.public.sellwellautoUrl}/logo.webp`,
  description: '赛沃车联专注于二手车出口，连接车源方、外贸商、海外买家，让出口更简单、更高效',
  sameAs: [config.public.sellwellautoUrl],
}

// 在组件挂载后添加结构化数据
onMounted(() => {
  if (import.meta.client) {
    // 添加结构化数据到页面
    const script = document.createElement('script')
    script.type = 'application/ld+json'
    script.textContent = JSON.stringify(structuredData)
    document.head.appendChild(script)
  }
})
</script>

<style lang="scss" scoped>
.app-logo {
  display: flex;
  align-items: center;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
  transition: opacity 0.3s ease;
}

.logo-image {
  height: 20px;
  width: auto;
  object-fit: contain;
  max-width: 100%;
}

// 屏幕阅读器专用样式
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>
