<template>
  <div class="user-section">
    <!-- 未登录状态 -->
    <template v-if="!userStore.isLoggedIn">
      <el-button type="primary" class="login-btn" @click="handleLogin"
        >{{ $t('common.login') }} / {{ $t('common.register') }}</el-button
      >
    </template>

    <!-- 已登录状态 -->
    <template v-else>
      <div class="user-profile">
        <el-icon class="user-icon">
          <User />
        </el-icon>
        <span class="username">{{ userStore.userInfo.username }}</span>
        <el-icon class="arrow-icon">
          <ArrowDown />
        </el-icon>

        <!-- 用户下拉菜单 -->
        <div class="user-dropdown-menu">
          <div class="dropdown-divider" />
          <div class="dropdown-item logout-item" @click="handleLogout">
            <el-icon class="item-icon">
              <SwitchButton />
            </el-icon>
            <span>{{ $t('userSection.logout') }}</span>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
// Element Plus图标导入
import { User, ArrowDown, SwitchButton } from '@element-plus/icons-vue'

const userStore = useUserStore()

const handleGetQuote = () => {}

const handleLogin = () => {
  navigateTo('/login')
}

const handleLogout = () => {
  userStore.clearUserInfo()
}

// 暴露方法供父组件调用
defineExpose({
  handleGetQuote,
  handleLogin,
  handleLogout,
})
</script>

<style lang="scss" scoped>
.user-section {
  @include flex-center;
  gap: $spacing-lg;
  position: relative;
}

.user-profile {
  @include flex-center;
  gap: $spacing-sm;
  cursor: pointer;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-base;
  transition: background-color 0.3s ease;
  position: relative;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);

    .user-dropdown-menu {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .arrow-icon {
      transform: rotate(180deg);
    }
  }
}

.user-icon {
  font-size: 16px;
}

.username {
  font-size: $font-size-small;
}

.arrow-icon {
  font-size: 10px;
  transition: transform 0.3s ease;

  &.rotated {
    transform: rotate(180deg);
  }
}

.login-btn {
  background-color: $primary-color;
  border-color: $primary-color;

  &:hover {
    background-color: #e55a2b;
    border-color: #e55a2b;
  }
}

.user-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-large;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-width: 120px;
  padding: $spacing-sm 0;
  z-index: 1001;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 4px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm $spacing-md;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: $font-size-base;

  &:hover {
    background: linear-gradient(90deg, rgba(255, 107, 53, 0.2), rgba(255, 140, 66, 0.2));
    color: $primary-color;
  }

  &.logout-item {
    color: #f56c6c;

    &:hover {
      background: linear-gradient(90deg, rgba(245, 108, 108, 0.2), rgba(245, 108, 108, 0.1));
      color: #f56c6c;
    }
  }
}

.item-icon {
  font-size: 14px;
}

.dropdown-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: $spacing-xs 0;
}

// 响应式设计
@include respond-to(sm) {
  .user-section {
    gap: $spacing-sm;
  }

  .workbench-text {
    display: none;
  }
}
</style>
