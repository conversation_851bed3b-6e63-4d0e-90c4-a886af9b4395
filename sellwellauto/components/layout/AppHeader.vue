<template>
  <header class="app-header">
    <div class="header-container">
      <!-- Logo -->
      <AppLogo />

      <!-- 导航菜单 -->
      <nav class="nav-menu">
        <ul class="nav-list">
          <li
            v-for="item in navItems"
            :key="item.key"
            class="nav-item"
            :class="{ active: activeNavKey === item.key }"
          >
            <div class="nav-link" @click="handleNavClick(item.path)">
              {{ item.label }}
              <el-icon v-if="item.children" class="arrow-icon">
                <ArrowDown />
              </el-icon>
            </div>

            <!-- 下拉菜单 -->
            <div v-if="item.children" class="dropdown-menu">
              <div
                v-for="child in item.children"
                :key="child.key"
                class="dropdown-item"
                :class="{ active: activeChildKey === child.key }"
                @click="handleNavClick(child.path)"
              >
                {{ child.label }}
              </div>
            </div>
          </li>
        </ul>
      </nav>

      <!-- 用户信息和操作 -->
      <div class="header-actions">
        <LanguageSwitcher />
        <UserSection />
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ArrowDown } from '@element-plus/icons-vue'

const route = useRoute()
const { t } = useI18n()

// 导航菜单数据 - 使用国际化文本
const navItems = computed(() => [
  { key: 'home', label: t('navigation.home'), path: '/' },
  {
    key: 'user',
    label: t('navigation.userCenter'),
    path: '/user/car-source',
    children: [
      { key: 'car-source', label: t('navigation.carSource'), path: '/user/car-source' },
      { key: 'trader', label: t('navigation.trader'), path: '/user/trader' },
      { key: 'overseas-buyer', label: t('navigation.overseasBuyer'), path: '/user/overseas-buyer' },
    ],
  },
  {
    key: 'about',
    label: t('navigation.about'),
    path: '/about/company',
    children: [
      { key: 'company', label: t('navigation.company'), path: '/about/company' },
      { key: 'news', label: t('navigation.news'), path: '/about/news' },
    ],
  },
])

// 计算当前激活的导航项
const activeNavKey = computed(() => {
  const currentPath = route.path

  // 检查是否是首页
  if (currentPath === '/') {
    return 'home'
  }

  // 检查每个导航项及其子项
  for (const item of navItems.value) {
    // 检查主路径（精确匹配）
    if (currentPath === item.path) {
      return item.key
    }

    // 检查子路径
    if (item.children) {
      for (const child of item.children) {
        if (currentPath === child.path) {
          return item.key // 返回父项的key
        }
      }
    }
  }

  // 特殊处理：新闻详情页面应该高亮"关于"导航
  if (currentPath.includes('/about/news-detail/')) {
    return 'about'
  }

  return ''
})

// 计算当前激活的子菜单项
const activeChildKey = computed(() => {
  const currentPath = route.path

  for (const item of navItems.value) {
    if (item.children) {
      for (const child of item.children) {
        if (currentPath === child.path) {
          return child.key
        }
      }
    }
  }

  return null
})

const handleNavClick = (path: string) => {
  navigateTo(path)
}
</script>

<style lang="scss" scoped>
.app-header {
  background-color: #000;
  color: white;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2001;
}

.header-container {
  max-width: $max-width;
  margin: 0 auto;
  padding: 0 $spacing-lg;
  @include flex-between;
  height: 64px;
}

.nav-menu {
  flex: 1;
  margin: 0 $spacing-xl;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.nav-list {
  @include flex-center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: $spacing-lg;
}

.nav-item {
  position: relative;
  cursor: pointer;

  &:hover {
    .dropdown-menu {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .nav-link {
      color: $primary-color;
    }

    .arrow-icon {
      transform: rotate(180deg);
      color: $primary-color;
    }
  }

  &.active {
    .nav-link {
      color: $primary-color;

      &::after {
        width: 80%;
      }
    }
  }
}

.nav-link {
  @include flex-center;
  gap: $spacing-xs;
  padding: $spacing-md $spacing-lg;
  border-radius: $border-radius-large;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  font-size: $font-size-base;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, $primary-color, #ff8c42);
    transition: all 0.3s ease;
    transform: translateX(-50%);
  }

  &:hover {
    background-color: rgba(255, 107, 53, 0.1);

    &::after {
      width: 80%;
    }
  }
}

.arrow-icon {
  font-size: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-left: 2px;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-large;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-width: 180px;
  padding: $spacing-sm 0;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 4px;
}

.dropdown-item {
  padding: $spacing-md $spacing-lg;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: $font-size-base;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 0;
    background: linear-gradient(180deg, $primary-color, #ff8c42);
    transition: height 0.3s ease;
  }

  &:hover {
    background: linear-gradient(90deg, rgba(255, 107, 53, 0.2), rgba(255, 140, 66, 0.2));
    color: $primary-color;
    padding-left: $spacing-xl;

    &::before {
      height: 100%;
    }
  }

  &.active {
    background: linear-gradient(90deg, rgba(255, 107, 53, 0.3), rgba(255, 140, 66, 0.3));
    color: $primary-color;
    padding-left: $spacing-xl;

    &::before {
      height: 100%;
    }
  }
}

// 响应式设计
@include respond-to(sm) {
  .header-container {
    padding: 0 $spacing-sm;
  }

  .nav-menu {
    display: none;
  }
}
</style>
