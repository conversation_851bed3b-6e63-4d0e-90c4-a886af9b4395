{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev --host", "generate": "nuxt generate", "preview": "npm run build && wrangler dev", "postinstall": "nuxt prepare", "deploy": "npm run build && wrangler deploy", "cf-typegen": "wrangler types", "lint": "eslint . --fix", "format": "prettier --write .", "type-check": "nuxi typecheck"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@element-plus/nuxt": "^1.0.10", "@nuxtjs/i18n": "^10.0.6", "@pinia/nuxt": "^0.8.0", "@types/js-cookie": "^3.0.6", "axios": "^1.11.0", "dayjs": "^1.11.13", "element-plus": "^2.10.5", "flag-icons": "^7.5.0", "js-cookie": "^3.0.5", "marked": "^16.2.1", "nuxt": "^4.0.0", "pinia": "^2.2.6", "swiper": "^11.2.10", "vue": "^3.5.18", "vue-router": "^4.5.1", "vue3-marquee": "^4.2.2"}, "devDependencies": {"@nuxt/devtools": "^1.6.0", "@nuxt/eslint": "^0.5.7", "@types/marked": "^5.0.2", "@types/node": "^22.16.5", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "eslint": "^9.31.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.31.0", "nitro-cloudflare-dev": "^0.2.2", "nitropack": "^2.12.6", "prettier": "^3.6.2", "sass": "^1.90.0", "typescript": "^5.8.4", "vue-tsc": "^2.1.10", "wrangler": "^4.36.0"}}