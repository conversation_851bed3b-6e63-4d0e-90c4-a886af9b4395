@use './variables' as *;
@use './mixins' as *;

// 导入 flag-icons
@import 'flag-icons/css/flag-icons.min.css';

// 全局重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: $font-size-base;
  line-height: 1.5;
  color: $text-primary;
  background-color: $background-color-base;
  padding: 0 !important;
  margin: 0 !important;
}

#__nuxt {
  height: 100%;
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mt-xs {
  margin-top: $spacing-xs;
}

.mt-sm {
  margin-top: $spacing-sm;
}

.mt-md {
  margin-top: $spacing-md;
}

.mt-lg {
  margin-top: $spacing-lg;
}

.mt-xl {
  margin-top: $spacing-xl;
}

.mb-xs {
  margin-bottom: $spacing-xs;
}

.mb-sm {
  margin-bottom: $spacing-sm;
}

.mb-md {
  margin-bottom: $spacing-md;
}

.mb-lg {
  margin-bottom: $spacing-lg;
}

.mb-xl {
  margin-bottom: $spacing-xl;
}

.ml-xs {
  margin-left: $spacing-xs;
}

.ml-sm {
  margin-left: $spacing-sm;
}

.ml-md {
  margin-left: $spacing-md;
}

.ml-lg {
  margin-left: $spacing-lg;
}

.ml-xl {
  margin-left: $spacing-xl;
}

.mr-xs {
  margin-right: $spacing-xs;
}

.mr-sm {
  margin-right: $spacing-sm;
}

.mr-md {
  margin-right: $spacing-md;
}

.mr-lg {
  margin-right: $spacing-lg;
}

.mr-xl {
  margin-right: $spacing-xl;
}

.p-xs {
  padding: $spacing-xs;
}

.p-sm {
  padding: $spacing-sm;
}

.p-md {
  padding: $spacing-md;
}

.p-lg {
  padding: $spacing-lg;
}

.p-xl {
  padding: $spacing-xl;
}

// 弹性布局工具类
.flex {
  display: flex;
}

.flex-center {
  @include flex-center;
}

.flex-between {
  @include flex-between;
}

.flex-column {
  @include flex-column;
}

// 文本工具类
.text-ellipsis {
  @include text-ellipsis;
}

.text-primary {
  color: $text-primary;
}

.text-regular {
  color: $text-regular;
}

.text-secondary {
  color: $text-secondary;
}

.text-placeholder {
  color: $text-placeholder;
}

.layout-enter-active,
.layout-leave-active {
  transition: all 0.4s;
}

.layout-enter-from,
.layout-leave-to {
  filter: grayscale(1);
}

// 响应式工具类
@include respond-to(sm) {
  .hidden-sm {
    display: none !important;
  }
}

@include respond-to(md) {
  .hidden-md {
    display: none !important;
  }
}

@include respond-to(lg) {
  .hidden-lg {
    display: none !important;
  }
}