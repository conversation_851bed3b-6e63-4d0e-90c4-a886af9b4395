@use './variables' as *;

// 弹性布局混入
@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// 文本混入
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-ellipsis-multi($lines: 2) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $lines;
  overflow: hidden;
}

// 响应式混入
@mixin respond-to($breakpoint) {
  @if $breakpoint ==xs {
    @media (max-width: $breakpoint-xs) {
      @content;
    }
  }

  @else if $breakpoint ==sm {
    @media (max-width: $breakpoint-sm) {
      @content;
    }
  }

  @else if $breakpoint ==md {
    @media (max-width: $breakpoint-md) {
      @content;
    }
  }

  @else if $breakpoint ==lg {
    @media (max-width: $breakpoint-lg) {
      @content;
    }
  }

  @else if $breakpoint ==xl {
    @media (max-width: $breakpoint-xl) {
      @content;
    }
  }
}

// 卡片样式混入
@mixin card-style {
  background-color: white;
  border-radius: $border-radius-large;
  box-shadow: $box-shadow-light;
  padding: $spacing-lg;
}

// 按钮样式混入
@mixin button-style($color: $primary-color) {
  background-color: $color;
  border: none;
  border-radius: $border-radius-base;
  color: white;
  cursor: pointer;
  padding: $spacing-sm $spacing-md;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba($color, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

// 渐变背景混入
@mixin gradient-bg($start-color, $end-color, $direction: 135deg) {
  background: linear-gradient($direction, $start-color 0%, $end-color 100%);
}

// 清除浮动混入
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 绝对定位居中混入
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 滚动条样式混入
@mixin custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: $background-color-light;
  }

  &::-webkit-scrollbar-thumb {
    background: $border-color-base;
    border-radius: 3px;

    &:hover {
      background: $text-secondary;
    }
  }
}