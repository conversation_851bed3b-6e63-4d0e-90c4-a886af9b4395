// Element Plus 组件全局样式覆盖
@use './variables' as *;
@use './mixins' as *;

// ===== El-Collapse FAQ样式 =====
// 使用更高优先级来覆盖 Element Plus 默认样式
// 使用 body 前缀提高优先级
body {
  .el-collapse {
    border: none !important;
  }

  .el-collapse-item {
    &__header {
      padding: 0px 30px !important;
      height: 50px !important;
      background: white !important;
      border: none !important;
      font-size: 1.1rem !important;
      font-weight: 600 !important;

      &:hover {
        background: #f8f9fa !important;
      }
    }

    &__content {
      padding: 0 30px 25px !important;
      background: white !important;
      border: none !important;
    }

    &__arrow {
      color: $primary-color !important;
      font-size: 18px !important;
    }
  }
}

// 响应式样式
@include respond-to(md) {
  body {
    .el-collapse-item {
      &__header {
        padding: 20px !important;
      }

      &__content {
        padding: 0 20px 20px !important;
      }
    }
  }
}