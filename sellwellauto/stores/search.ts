import { defineStore } from 'pinia'

export interface SearchCondition {
  search_text?: string
  brand_id?: number | null
  series_id?: number | null
}

export const useSearchStore = defineStore('search', () => {
  // 搜索条件
  const searchCondition = ref<SearchCondition>({})

  // 是否来自首页搜索
  const isFromHomeSearch = ref(false)

  // 设置搜索条件
  const setSearchCondition = (condition: SearchCondition) => {
    searchCondition.value = { ...condition }
    isFromHomeSearch.value = true
  }

  // 清除搜索条件
  const clearSearchCondition = () => {
    searchCondition.value = {}
    isFromHomeSearch.value = false
  }

  // 获取搜索条件
  const getSearchCondition = () => {
    return { ...searchCondition.value }
  }

  return {
    searchCondition: readonly(searchCondition),
    isFromHomeSearch: readonly(isFromHomeSearch),
    setSearchCondition,
    clearSearchCondition,
    getSearchCondition
  }
})
