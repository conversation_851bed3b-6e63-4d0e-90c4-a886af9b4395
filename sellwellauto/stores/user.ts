import { defineStore } from 'pinia'
import type { User, UserInfo } from '~/types'

export const useUserStore = defineStore('user', () => {
  // 使用 Nuxt Cookie 进行状态管理（服务端和客户端同步）
  const userInfoCookie = useCookie<User | null>('userInfo', {
    default: () => null,
    maxAge: 60 * 60 * 24 * 7, // 7天
    httpOnly: false, // 允许客户端访问
    secure: process.env.NODE_ENV === 'production', // 生产环境启用 HTTPS
    sameSite: 'lax' // 改为 lax 以确保跨页面导航时 Cookie 可用
  })

  const tokenCookie = useCookie<string>('token', {
    default: () => '',
    maxAge: 60 * 60 * 24 * 7, // 7天
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax'
  })

  // 响应式状态（从 Cookie 直接获取）
  const rawUserInfo = computed<User | null>(() => {
    return userInfoCookie.value
  })

  const token = computed(() => tokenCookie.value || '')

  // 计算属性
  const userInfo = computed((): UserInfo => {
    if (!rawUserInfo.value) {
      return {
        id: 0,
        username: '',
        account: '',
        nickname: '',
        f_belong_company: ''
      }
    }

    return {
      id: rawUserInfo.value.id || 0,
      username: rawUserInfo.value.username || rawUserInfo.value.nickname || '',
      account: rawUserInfo.value.username || rawUserInfo.value.account || '',
      nickname: rawUserInfo.value.nickname || rawUserInfo.value.username || '',
      f_belong_company: rawUserInfo.value.f_belong_company || ''
    }
  })

  // 登录状态（基于 Cookie 计算）
  const isLoggedIn = computed(() => {
    return !!rawUserInfo.value && !!token.value
  })

  // 初始化用户信息（Cookie 自动处理，无需手动初始化）
  const initUserInfo = () => {
    // Cookie 状态自动同步，无需手动操作
  }

  // 设置用户信息（直接更新 Cookie）
  const setUserInfo = (info: User) => {
    userInfoCookie.value = info
    tokenCookie.value = info.token || ''
  }

  // 清除用户信息（清除 Cookie）
  const clearUserInfo = () => {
    userInfoCookie.value = null
    tokenCookie.value = ''
  }

  return {
    rawUserInfo: readonly(rawUserInfo),
    userInfo,
    token: readonly(token),
    isLoggedIn,
    initUserInfo,
    setUserInfo,
    clearUserInfo
  }
})
