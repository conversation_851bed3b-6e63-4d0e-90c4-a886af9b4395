// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },

  nitro: {
    preset: 'cloudflare_module',

    cloudflare: {
      deployConfig: true,
      nodeCompat: true
    },

    devProxy: {
      '/api': {
        target: 'http://**************:4321/api',
        changeOrigin: true
      }
    }
  },

  // Nuxt 4 新特性
  future: {
    compatibilityVersion: 4
  },

  // CSS框架和样式配置
  css: ['@/assets/styles/global.scss', '@/assets/styles/element-plus.scss'],

  // 模块配置
  modules: [
    'nitro-cloudflare-dev',
    '@element-plus/nuxt',
    '@pinia/nuxt',
    '@nuxt/eslint',
    '@nuxtjs/i18n'
  ],

  // 组件自动导入配置
  components: [
    {
      path: '~/components',
      pathPrefix: false
    }
  ],

  // Element Plus配置
  elementPlus: {
    importStyle: 'scss'
  },

  // TypeScript配置
  typescript: {
    typeCheck: false
  },

  // Vite配置
  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
            @use "@/assets/styles/variables" as *;
            @use "@/assets/styles/mixins" as *;
            @use "@/assets/css/element-plus-index.scss" as *;
          `
        }
      }
    }
  },

  // 运行时配置
  runtimeConfig: {
    public: {
      lafApiBase: 'https://laf.we5.fun', // LAF API 基础 URL (用于新闻列表等)
      sellwellautoApiBase: 'https://erp.sellwelltech.com/api', // 赛沃车联 ERP API 基础 URL (用于用户、订单等)
      sellwellautoServerApiBase: 'http://**************:4321/api',
      // sellwellautoServerApiBase: 'https://api.sellwellauto.com/api',
      erpUrl: 'https://erp.sellwelltech.com',
      sellwellautoUrl: 'https://www.sellwellauto.com'
    }
  },

  // 应用配置
  app: {
    head: {
      titleTemplate: '%s - 赛沃车联 Sellwell Auto',
      title: '赛沃车联',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        {
          name: 'description',
          content:
            '赛沃车联专注于二手车出口，连接车源方、外贸商、海外买家，让出口更简单、更高效'
        },
        {
          name: 'keywords',
          content: '赛沃车联,Sellwell auto,二手车,二手车出口平台'
        }
      ],
      link: [{ rel: 'icon', type: 'image/png', href: '/favicon.png' }]
    },
    pageTransition: { name: 'page', mode: 'out-in' }
  },

  // 构建配置
  build: {
    transpile: ['element-plus']
  },

  // 服务端渲染配置
  ssr: true,

  // 路由配置
  router: {
    options: {
      scrollBehaviorType: 'smooth'
    }
  },

  // 开发服务器配置
  devServer: {
    host: '0.0.0.0',
    port: 3000
  },

  // 国际化配置 - Nuxt 4 兼容配置
  i18n: {
    langDir: './locales',
    locales: [
      {
        code: 'zh-CN',
        iso: 'zh-CN',
        name: '简体中文',
        file: 'zh-CN.json',
        flag: '🇨🇳'
      },
      {
        code: 'en',
        iso: 'en-US',
        name: 'English',
        file: 'en.json',
        flag: '🇺🇸'
      }
    ],
    vueI18n: './i18n.config.ts',
    defaultLocale: 'en',
    strategy: 'no_prefix',
    detectBrowserLanguage: false
  }
})
