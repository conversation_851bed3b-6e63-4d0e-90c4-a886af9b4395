export default defineNuxtRouteMiddleware(() => {
  // 只在服务端运行
  if (import.meta.client) return

  const event = useRequestEvent()
  if (!event) return

  const region = (event as any).context?.region
  if (region?.finalLocale) {
    const nuxtApp = useNuxtApp()
    const i18n: any = nuxtApp.$i18n

    // 确保语言设置正确
    if (i18n && i18n.locale && i18n.locale.value !== region.finalLocale) {
      console.log(`[i18n-middleware] 设置语言: -> ${region.finalLocale}`)
      i18n.setLocale(region.finalLocale)
    }
  } else {
    // 如果没有地区检测结果，确保使用默认语言
    const nuxtApp = useNuxtApp()
    const i18n: any = nuxtApp.$i18n

    if (i18n && i18n.locale) {
      const defaultLocale = 'en'
      if (i18n.locale.value !== defaultLocale) {
        console.log(`[i18n-middleware] 设置默认语言:  ${defaultLocale}`)
        i18n.setLocale(defaultLocale)
      }
    }
  }
})
