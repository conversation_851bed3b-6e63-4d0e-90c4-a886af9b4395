<template>
  <div class="login-page">
    <!-- 左侧图片区域 -->
    <div class="left-section">
      <img src="/images/login-bg.png" alt="login-bg" />
    </div>

    <!-- 右侧表单区域 -->
    <div class="right-section">
      <div class="form-container">
        <!-- 切换按钮 -->
        <div class="form-tabs">
          <el-button
            :type="activeTab === 'login' ? 'primary' : 'default'"
            class="tab-btn"
            @click="activeTab = 'login'"
          >
            {{ $t('login.tabs.login') }}
          </el-button>
          <el-button
            :type="activeTab === 'register' ? 'primary' : 'default'"
            class="tab-btn"
            @click="activeTab = 'register'"
          >
            {{ $t('login.tabs.register') }}
          </el-button>
        </div>

        <!-- 登录表单组件 -->
        <LoginForm v-show="activeTab === 'login'" />

        <!-- 注册表单组件 -->
        <RegisterForm v-show="activeTab === 'register'" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute()
const activeTab = ref('login')

// 组件挂载时检查是否有跳转参数
onMounted(() => {
  // 如果有redirect参数，保存到sessionStorage中
  if (route.query.redirect) {
    if (import.meta.client) {
      sessionStorage.setItem('loginRedirect', route.query.redirect as string)
    }
  }
})

// SEO 配置
useHead({
  title: $t('login.pageTitle'),
  meta: [
    {
      name: 'description',
      content: $t('login.pageDescription'),
    },
    { name: 'keywords', content: $t('login.pageKeywords') },
  ],
})
</script>

<style lang="scss" scoped>
@use '../assets/styles/variables' as *;
@use '../assets/styles/mixins' as *;

.login-page {
  height: 100vh;
  display: flex;
  overflow: hidden;
}

.left-section {
  // flex: 1;
  width: 60%;
  height: 100vh;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}

.right-section {
  // flex: 1;
  width: 40%;
  height: 100vh;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-container {
  width: 100%;
  max-width: 400px;
}

.form-tabs {
  display: flex;
  margin-bottom: $spacing-xl;
  background: #f5f5f5;
  border-radius: $border-radius-base;
  padding: $spacing-xs;
}

.tab-btn {
  flex: 1;
  border: none;
  background: transparent;
  border-radius: $border-radius-base;
  transition: all 0.3s ease;

  &.el-button--primary {
    background: white;
    color: $text-primary;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@include respond-to(sm) {
  .login-page {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .left-section {
    width: 100%;
    height: 40vh; // 小屏幕下图片区域占40%高度
    min-height: 200px;
  }

  .right-section {
    width: 100%;
    height: auto;
    min-height: 60vh;
    padding: $spacing-lg;
    backdrop-filter: none; // 小屏幕下移除模糊效果
    background: rgba(255, 255, 255, 0.95); // 添加半透明背景
  }

  .form-container {
    max-width: 100%;
    padding: 0 $spacing-md;
  }
}

@include respond-to(xs) {
  .left-section {
    height: 35vh; // 超小屏幕下图片区域占35%高度
    min-height: 180px;
  }

  .right-section {
    min-height: 65vh;
    padding: $spacing-md;
  }

  .form-container {
    padding: 0 $spacing-sm;
  }

  .form-tabs {
    margin-bottom: $spacing-lg;
  }
}
</style>
