<template>
  <div class="overseas-buyer">
    <!-- Hero区域 -->
    <div class="banner-image">
      <img
        src="/images/overseasBuyer/banner.png"
        :alt="$t('overseasBuyer.bannerAlt')"
        class="w-full h-auto object-cover"
      />
    </div>

    <!-- 赛沃车联为海外买家打造三重安心体验 -->
    <div class="buyer-benefits-section max-container">
      <div class="title">{{ $t('overseasBuyer.buyerBenefits.title') }}</div>

      <div class="buyer-benefits-list">
        <div
          v-for="(item, index) in buyerBenefits"
          :key="index"
          class="buyer-benefits-item"
          :class="{ reverse: index != 1 }"
        >
          <div class="buyer-benefits-item-content">
            <div class="buyer-benefits-item-title">{{ item.title }}</div>
            <div class="buyer-benefits-item-points">
              <div
                v-for="(point, pointIndex) in item.points"
                :key="pointIndex"
                class="buyer-benefits-item-point"
              >
                {{ point }}
              </div>
            </div>
            <ActionButton
              :text="$t('overseasBuyer.buyerBenefits.contactButton')"
              custom-class="register-btn"
            />
          </div>

          <div class="buyer-benefits-item-image">
            <img :src="item.image" :alt="item.title" />
          </div>
        </div>
      </div>
    </div>

    <!-- FAQ区域 -->
    <div class="faq-section max-container">
      <div class="title">{{ $t('overseasBuyer.faq.title') }}</div>
      <div class="faq-list">
        <el-collapse v-model="activeNames">
          <el-collapse-item
            v-for="(faq, index) in faqList"
            :key="index"
            :name="index"
            class="faq-item"
          >
            <template #title>
              <span class="faq-question">
                <span class="question-dot" />
                {{ faq.question }}
              </span>
            </template>
            <div class="faq-answer">
              <p>{{ faq.answer }}</p>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面SEO设置
useHead({
  title: $t('overseasBuyer.pageTitle'),
  meta: [
    {
      name: 'description',
      content: $t('overseasBuyer.pageDescription'),
    },
    {
      name: 'keywords',
      content: $t('overseasBuyer.pageKeywords'),
    },
  ],
})

// 页面元数据
definePageMeta({
  layout: 'default',
})

const { t } = useI18n()

// 海外买家三重安心体验数据
const buyerBenefits = computed(() => [
  {
    title: t('overseasBuyer.buyerBenefits.items.authenticSource.title'),
    points: [t('overseasBuyer.buyerBenefits.items.authenticSource.point1')],
    image: '/images/overseasBuyer/list_1.png',
  },
  {
    title: t('overseasBuyer.buyerBenefits.items.transparentTransaction.title'),
    points: [t('overseasBuyer.buyerBenefits.items.transparentTransaction.point1')],
    image: '/images/overseasBuyer/list_2.png',
  },
  {
    title: t('overseasBuyer.buyerBenefits.items.noLanguageBarrier.title'),
    points: [t('overseasBuyer.buyerBenefits.items.noLanguageBarrier.point1')],
    image: '/images/overseasBuyer/list_3.png',
  },
])

// FAQ数据
const faqList = computed(() => [
  {
    question: t('overseasBuyer.faq.questions.authenticSource.question'),
    answer: t('overseasBuyer.faq.questions.authenticSource.answer'),
  },
  {
    question: t('overseasBuyer.faq.questions.transactionSecurity.question'),
    answer: t('overseasBuyer.faq.questions.transactionSecurity.answer'),
  },
  {
    question: t('overseasBuyer.faq.questions.exportCompliance.question'),
    answer: t('overseasBuyer.faq.questions.exportCompliance.answer'),
  },
])

// Element Plus collapse 控制
const activeNames = ref([])
</script>

<style lang="scss" scoped>
.overseas-buyer {
  background-color: #fff;

  .banner-image {
    img {
      width: 100%;
    }
  }

  .max-container {
    max-width: $max-width;
    margin: 0 auto;
  }

  .title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #141414;
    margin-bottom: 30px;
    text-align: center;
  }

  .buyer-benefits-section {
    padding: 60px 0px;

    .title {
      font-size: 2.5rem;
      font-weight: bold;
      color: #141414;
      margin-bottom: 60px;
      text-align: center;
    }

    .buyer-benefits-list {
      display: flex;
      flex-direction: column;
      gap: 30px;
    }

    .buyer-benefits-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 0px;

      &.reverse {
        flex-direction: row-reverse;
      }
    }

    .buyer-benefits-item-content {
      flex: 1;
    }

    .buyer-benefits-item-title {
      font-size: 1.6rem;
      font-weight: bold;
      color: #616161;
      margin-bottom: 40px;
      position: relative;
      margin-left: 30px;
    }

    .buyer-benefits-item-title {
      &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 20px;
        height: 3px;
        background: $primary-color;
        border-radius: 4px;
      }
    }

    .buyer-benefits-item-points {
      margin-bottom: 30px;
      margin-left: 30px;
    }

    .register-btn {
      background: $primary-color;
      color: white;
      border: none;
      padding: 6px 24px;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s ease;
      margin-left: 30px;

      &:hover {
        background: #e55a2b;
      }
    }

    .buyer-benefits-item-point {
      font-size: 1.2rem;
      color: #616161;
      margin-bottom: 24px;
      position: relative;
    }

    .buyer-benefits-item-image {
      flex-shrink: 0;
      flex: 1;
      overflow: hidden;
      border-radius: 4px;

      img {
        width: 100%;
        height: auto;
        border-radius: 4px;
        transition: transform 0.3s ease;
        cursor: pointer;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    // 响应式设计
    @include respond-to(md) {
      padding: 40px 20px;

      .title {
        font-size: 2rem;
        margin-bottom: 40px;
      }

      .buyer-benefits-item {
        flex-direction: column !important;
        padding: 30px 0;
        gap: 30px;

        &.reverse {
          flex-direction: column !important;
        }
      }

      .buyer-benefits-item-title {
        font-size: 1.5rem;
        margin-left: 0;

        &::after {
          left: 0;
          transform: translateX(0);
        }
      }

      .buyer-benefits-item-point {
        padding-left: 0;

        &::before {
          position: static;
          margin-right: 8px;
        }
      }

      .buyer-benefits-item-points {
        margin-left: 0;
      }

      .register-btn {
        margin-left: 0;
      }

      .buyer-benefits-item-image {
        img {
          width: 100%;
        }
      }
    }
  }

  .faq-section {
    padding: 80px 0;
    max-width: $max-width;
    margin: 0 auto;

    .title {
      font-size: 2.5rem;
      font-weight: bold;
      color: #141414;
      margin-bottom: 60px;
      text-align: center;
    }

    .faq-list {
      max-width: 800px;
      margin: 0 auto;
    }

    .faq-item {
      margin-bottom: 20px;
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      }
    }

    .faq-question {
      font-size: 1.1rem;
      font-weight: 600;
      color: #999;
      line-height: 1.5;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .question-dot {
      width: 6px;
      height: 6px;
      background-color: #999;
      border-radius: 50%;
      flex-shrink: 0;
    }

    .faq-answer {
      padding: 20px 0;
      color: #666;
      line-height: 1.6;
    }

    // 响应式设计
    @include respond-to(md) {
      padding: 40px 20px;

      .title {
        font-size: 2rem;
        margin-bottom: 40px;
      }

      .faq-list {
        max-width: 100%;
      }

      .faq-question {
        font-size: 1rem;
      }
    }
  }
}
</style>
