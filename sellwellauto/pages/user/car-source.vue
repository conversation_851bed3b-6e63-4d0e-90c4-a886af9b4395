<template>
  <div class="car-source">
    <!-- Hero区域 -->
    <div class="banner-image">
      <img
        src="/images/carSource/banner.png"
        :alt="$t('carSource.bannerAlt')"
        class="w-full h-auto object-cover"
      />
    </div>

    <!-- 三大核心价值区域 -->
    <div class="core-values-section max-container">
      <div class="title">{{ $t('carSource.coreValues.title') }}</div>

      <div class="core-values-list">
        <div
          v-for="(item, index) in coreValues"
          :key="index"
          class="core-values-item"
          :class="{ reverse: index === 1 }"
        >
          <div class="core-values-item-content">
            <div class="core-values-item-title">{{ item.title }}</div>
            <div class="core-values-item-points">
              <div
                v-for="(point, pointIndex) in item.points"
                :key="pointIndex"
                class="core-values-item-point"
              >
                {{ point }}
              </div>
            </div>
            <ActionButton
              :text="$t('carSource.coreValues.registerButton')"
              custom-class="register-btn"
            />
          </div>

          <div class="core-values-item-image">
            <img :src="item.image" :alt="item.title" />
          </div>
        </div>
      </div>
    </div>

    <!-- FAQ区域 -->
    <div class="faq-section max-container">
      <div class="title">{{ $t('carSource.faq.title') }}</div>
      <div class="faq-list">
        <el-collapse v-model="activeNames">
          <el-collapse-item
            v-for="(faq, index) in faqList"
            :key="index"
            :name="index"
            class="faq-item"
          >
            <template #title>
              <span class="faq-question">
                <span class="question-dot" />
                {{ faq.question }}
              </span>
            </template>
            <div class="faq-answer">
              <p>{{ faq.answer }}</p>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面SEO设置
useHead({
  title: $t('carSource.pageTitle'),
  meta: [
    {
      name: 'description',
      content: $t('carSource.pageDescription'),
    },
    {
      name: 'keywords',
      content: $t('carSource.pageKeywords'),
    },
  ],
})

// 页面元数据
definePageMeta({
  layout: 'default',
})

const { t } = useI18n()

// 三大核心价值数据
const coreValues = computed(() => [
  {
    title: t('carSource.coreValues.items.findCustomers.title'),
    points: [
      t('carSource.coreValues.items.findCustomers.point1'),
      t('carSource.coreValues.items.findCustomers.point2'),
      t('carSource.coreValues.items.findCustomers.point3'),
    ],
    image: '/images/carSource/list_1.png',
  },
  {
    title: t('carSource.coreValues.items.expandMarket.title'),
    points: [
      t('carSource.coreValues.items.expandMarket.point1'),
      t('carSource.coreValues.items.expandMarket.point2'),
      t('carSource.coreValues.items.expandMarket.point3'),
    ],
    image: '/images/carSource/list_2.png',
  },
  {
    title: t('carSource.coreValues.items.quickListing.title'),
    points: [
      t('carSource.coreValues.items.quickListing.point1'),
      t('carSource.coreValues.items.quickListing.point2'),
    ],
    image: '/images/carSource/list_3.png',
  },
])

// FAQ数据
const faqList = computed(() => [
  {
    question: t('carSource.faq.questions.quickTransaction.question'),
    answer: t('carSource.faq.questions.quickTransaction.answer'),
  },
  {
    question: t('carSource.faq.questions.exportProcess.question'),
    answer: t('carSource.faq.questions.exportProcess.answer'),
  },
  {
    question: t('carSource.faq.questions.nicheModels.question'),
    answer: t('carSource.faq.questions.nicheModels.answer'),
  },
])

// Element Plus collapse 控制
const activeNames = ref([])
</script>

<style lang="scss" scoped>
.car-source {
  background-color: #fff;

  .banner-image {
    img {
      width: 100%;
    }
  }

  .max-container {
    max-width: $max-width;
    margin: 0 auto;
  }

  .title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #141414;
    margin-bottom: 30px;
    text-align: center;
  }

  .core-values-section {
    padding: 60px 0px;

    .title {
      font-size: 2.5rem;
      font-weight: bold;
      color: #141414;
      margin-bottom: 60px;
      text-align: center;
    }

    .core-values-list {
      display: flex;
      flex-direction: column;
      gap: 30px;
    }

    .core-values-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 0px;

      &.reverse {
        flex-direction: row-reverse;
      }
    }

    .core-values-item-content {
      flex: 1;
    }

    .core-values-item-title {
      font-size: 1.6rem;
      font-weight: bold;
      color: #616161;
      margin-bottom: 40px;
      position: relative;
      margin-left: 30px;
    }

    .core-values-item-title {
      &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 20px;
        height: 3px;
        background: $primary-color;
        border-radius: 4px;
      }
    }

    .core-values-item-points {
      margin-bottom: 30px;
      margin-left: 30px;
    }

    .register-btn {
      background: $primary-color;
      color: white;
      border: none;
      padding: 6px 24px;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s ease;
      margin-left: 30px;

      &:hover {
        background: #e55a2b;
      }
    }

    .core-values-item-point {
      font-size: 1.2rem;
      color: #616161;
      margin-bottom: 24px;
      position: relative;
      padding: 0 14px;

      &::before {
        content: '•';
        position: absolute;
        left: 0;
        font-weight: bold;
      }
    }

    .core-values-item-image {
      flex-shrink: 0;
      flex: 1;
      overflow: hidden;
      border-radius: 4px;

      img {
        width: 100%;
        height: auto;
        border-radius: 4px;
        transition: transform 0.3s ease;
        cursor: pointer;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    // 响应式设计
    @include respond-to(md) {
      padding: 40px 20px;

      .title {
        font-size: 2rem;
        margin-bottom: 40px;
      }

      .core-values-item {
        flex-direction: column;
        padding: 30px 0;
        gap: 30px;
      }

      .core-values-item-title {
        font-size: 1.5rem;
        margin-left: 0;

        &::after {
          left: 0;
          transform: translateX(0);
        }
      }

      .core-values-item-point {
        padding-left: 0;

        &::before {
          position: static;
          margin-right: 8px;
        }
      }

      .core-values-item-points {
        margin-left: 0;
      }

      .register-btn {
        margin-left: 0;
      }

      .core-values-item-image {
        img {
          width: 100%;
        }
      }
    }
  }

  .faq-section {
    padding: 80px 0;
    max-width: $max-width;
    margin: 0 auto;

    .title {
      font-size: 2.5rem;
      font-weight: bold;
      color: #141414;
      margin-bottom: 60px;
      text-align: center;
    }

    .faq-list {
      max-width: 800px;
      margin: 0 auto;
    }

    .faq-item {
      margin-bottom: 20px;
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      }
    }

    .faq-question {
      font-size: 1.1rem;
      font-weight: 600;
      color: #999;
      line-height: 1.5;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .question-dot {
      width: 6px;
      height: 6px;
      background-color: #999;
      border-radius: 50%;
      flex-shrink: 0;
    }

    .faq-answer {
      padding: 20px 0;
      color: #666;
      line-height: 1.6;
    }

    // 响应式设计
    @include respond-to(md) {
      padding: 40px 20px;

      .title {
        font-size: 2rem;
        margin-bottom: 40px;
      }

      .faq-list {
        max-width: 100%;
      }

      .faq-question {
        font-size: 1rem;
      }
    }
  }
}
</style>
