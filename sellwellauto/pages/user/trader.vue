<template>
  <div class="trader">
    <!-- Hero区域 -->
    <div class="banner-image">
      <img
        src="/images/trader/banner.png"
        :alt="$t('trader.bannerAlt')"
        class="w-full h-auto object-cover"
      />
    </div>

    <!-- 五大核心价值区域 -->
    <div class="trader-benefits-section max-container">
      <div class="title">{{ $t('trader.traderBenefits.title') }}</div>

      <!-- Tab导航 -->
      <div class="trader-tabs">
        <div
          v-for="(item, index) in traderBenefits"
          :key="index"
          class="trader-tab"
          :class="{ active: activeTab === index }"
          @click="scrollToSection(index)"
        >
          {{ $t(`trader.traderBenefits.tabTitles.${index}`) }}
        </div>
      </div>

      <div class="trader-benefits-list">
        <div
          v-for="(item, index) in traderBenefits"
          :key="index"
          class="trader-benefits-item"
          :class="{ reverse: [0, 2, 4].includes(index) }"
        >
          <div class="trader-benefits-item-content">
            <div class="trader-benefits-item-title">{{ item.title }}</div>
            <div class="trader-benefits-item-points">
              <div
                v-for="(point, pointIndex) in item.points"
                :key="pointIndex"
                class="trader-benefits-item-point"
                :class="{ 'has-bullet': item.points.length > 1 }"
              >
                {{ point }}
              </div>
            </div>
            <ActionButton
              :text="$t('trader.traderBenefits.freeTrialButton')"
              custom-class="register-btn"
            />
          </div>

          <div class="trader-benefits-item-image">
            <img :src="item.image" :alt="item.title" />
          </div>
        </div>
      </div>
    </div>

    <!-- FAQ区域 -->
    <div class="faq-section max-container">
      <div class="title">{{ $t('trader.faq.title') }}</div>
      <div class="faq-list">
        <el-collapse v-model="activeNames">
          <el-collapse-item
            v-for="(faq, index) in faqList"
            :key="index"
            :name="index"
            class="faq-item"
          >
            <template #title>
              <span class="faq-question">
                <span class="question-dot" />
                {{ faq.question }}
              </span>
            </template>
            <div class="faq-answer">
              <p>{{ faq.answer }}</p>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面SEO设置
useHead({
  title: $t('trader.pageTitle'),
  meta: [
    {
      name: 'description',
      content: $t('trader.pageDescription'),
    },
    {
      name: 'keywords',
      content: $t('trader.pageKeywords'),
    },
  ],
})

// 页面元数据
definePageMeta({
  layout: 'default',
})

const { t } = useI18n()

// 外贸商五大核心价值数据
const traderBenefits = computed(() => [
  {
    title: t('trader.traderBenefits.items.sufficientSource.title'),
    points: [t('trader.traderBenefits.items.sufficientSource.point1')],
    image: '/images/trader/list_1.png',
  },
  {
    title: t('trader.traderBenefits.items.trafficBoost.title'),
    points: [
      t('trader.traderBenefits.items.trafficBoost.point1'),
      t('trader.traderBenefits.items.trafficBoost.point2'),
    ],
    image: '/images/trader/list_2.png',
  },
  {
    title: t('trader.traderBenefits.items.quickSearch.title'),
    points: [t('trader.traderBenefits.items.quickSearch.point1')],
    image: '/images/trader/list_3.png',
  },
  {
    title: t('trader.traderBenefits.items.globalManagement.title'),
    points: [t('trader.traderBenefits.items.globalManagement.point1')],
    image: '/images/trader/list_4.png',
  },
  {
    title: t('trader.traderBenefits.items.easyToUse.title'),
    points: [t('trader.traderBenefits.items.easyToUse.point1')],
    image: '/images/trader/list_5.png',
  },
])

// FAQ数据
const faqList = computed(() => [
  {
    question: t('trader.faq.questions.findCarSource.question'),
    answer: t('trader.faq.questions.findCarSource.answer'),
  },
  {
    question: t('trader.faq.questions.makeQuotation.question'),
    answer: t('trader.faq.questions.makeQuotation.answer'),
  },
  {
    question: t('trader.faq.questions.whyJoinPlatform.question'),
    answer: t('trader.faq.questions.whyJoinPlatform.answer'),
  },
])

// Element Plus collapse 控制
const activeNames = ref([])

// Tab导航控制
const activeTab = ref(0)

// 滚动到指定区域
const scrollToSection = (index: number) => {
  activeTab.value = index
  if (import.meta.client) {
    const element = document.querySelector(`.trader-benefits-item:nth-child(${index + 1})`)
    if (element) {
      const elementTop = element.getBoundingClientRect().top + window.pageYOffset
      const offset = 140 // 距离元素140px的位置
      window.scrollTo({
        top: elementTop - offset,
        behavior: 'smooth',
      })
    }
  }
}

// 监听滚动事件，自动高亮对应Tab
const handleScroll = () => {
  if (!import.meta.client) return

  const scrollTop = window.pageYOffset
  const tabHeight = 140 // Tab导航的高度，包括偏移量

  // 遍历所有内容区域，找到当前可见的区域
  for (let i = 0; i < traderBenefits.value.length; i++) {
    const element = document.querySelector(
      `.trader-benefits-item:nth-child(${i + 1})`
    ) as HTMLElement
    if (element) {
      const elementTop = element.offsetTop - tabHeight
      const elementBottom = elementTop + element.offsetHeight

      // 如果当前滚动位置在这个区域内，就高亮对应的Tab
      if (scrollTop >= elementTop && scrollTop < elementBottom) {
        activeTab.value = i
        break
      }
    }
  }
}

// 组件挂载时添加滚动监听
onMounted(() => {
  if (import.meta.client) {
    window.addEventListener('scroll', handleScroll)
  }
})

// 组件卸载时移除滚动监听
onUnmounted(() => {
  if (import.meta.client) {
    window.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style lang="scss" scoped>
.trader {
  background-color: #fff;

  .banner-image {
    position: relative;

    img {
      width: 100%;
    }
  }

  .max-container {
    max-width: $max-width;
    margin: 0 auto;
  }

  .title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #141414;
    margin-bottom: 30px;
    text-align: center;
  }

  .trader-benefits-section {
    padding: 60px 0px;

    .title {
      font-size: 2.5rem;
      font-weight: bold;
      color: #141414;
      margin-bottom: 60px;
      text-align: center;
    }

    .trader-tabs {
      position: sticky;
      top: 64px;
      z-index: 100;
      background: white;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .trader-tab {
      flex: 1;
      text-align: center;
      padding: 12px 0px;
      background: #fff;
      cursor: pointer;
      font-size: 1.2rem;
      font-weight: 600;
      color: #999;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:hover {
        background: $primary-color;
        color: white;
      }

      &.active {
        background: $primary-color;
        color: white;
        border-color: $primary-color;
        box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
      }
    }

    .trader-benefits-list {
      margin-top: 60px;
      display: flex;
      flex-direction: column;
      gap: 30px;
    }

    .trader-benefits-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 0px;

      &.reverse {
        flex-direction: row-reverse;
      }
    }

    .trader-benefits-item-content {
      flex: 1;
    }

    .trader-benefits-item-title {
      font-size: 1.6rem;
      font-weight: bold;
      color: #616161;
      margin-bottom: 40px;
      position: relative;
      margin-left: 30px;
    }

    .trader-benefits-item-title {
      &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 20px;
        height: 3px;
        background: $primary-color;
        border-radius: 4px;
      }
    }

    .trader-benefits-item-points {
      margin-bottom: 30px;
      margin-left: 30px;
    }

    .register-btn {
      background: $primary-color;
      color: white;
      border: none;
      padding: 6px 24px;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s ease;
      margin-left: 30px;

      &:hover {
        background: #e55a2b;
      }
    }

    .trader-benefits-item-point {
      font-size: 1.2rem;
      color: #616161;
      margin-bottom: 24px;
      position: relative;
      padding-left: 0;

      &.has-bullet {
        padding-left: 14px;

        &::before {
          content: '•';
          position: absolute;
          left: 0;
          font-weight: bold;
        }
      }
    }

    .trader-benefits-item-image {
      flex-shrink: 0;
      flex: 1;
      overflow: hidden;
      border-radius: 4px;

      img {
        width: 100%;
        height: auto;
        border-radius: 4px;
        transition: transform 0.3s ease;
        cursor: pointer;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    // 响应式设计
    @include respond-to(md) {
      padding: 40px 20px;

      .title {
        font-size: 2rem;
        margin-bottom: 40px;
      }

      .trader-tabs {
        gap: 10px;
        margin-bottom: 40px;
        padding: 15px 0;
      }

      .trader-tab {
        padding: 8px 16px;
        font-size: 0.9rem;
      }

      .trader-benefits-item {
        flex-direction: column !important;
        padding: 30px 0;
        gap: 30px;

        &.reverse {
          flex-direction: column !important;
        }
      }

      .trader-benefits-item-title {
        font-size: 1.5rem;
        margin-left: 0;

        &::after {
          left: 0;
          transform: translateX(0);
        }
      }

      .trader-benefits-item-point {
        padding-left: 0;

        &.has-bullet {
          padding-left: 0;

          &::before {
            position: static;
            margin-right: 8px;
          }
        }
      }

      .trader-benefits-item-points {
        margin-left: 0;
      }

      .register-btn {
        margin-left: 0;
      }

      .trader-benefits-item-image {
        img {
          width: 100%;
        }
      }
    }
  }

  .faq-section {
    padding: 80px 0;
    max-width: $max-width;
    margin: 0 auto;

    .title {
      font-size: 2.5rem;
      font-weight: bold;
      color: #141414;
      margin-bottom: 60px;
      text-align: center;
    }

    .faq-list {
      max-width: 800px;
      margin: 0 auto;
    }

    .faq-item {
      margin-bottom: 20px;
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      }
    }

    .faq-question {
      font-size: 1.1rem;
      font-weight: 600;
      color: #999;
      line-height: 1.5;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .question-dot {
      width: 6px;
      height: 6px;
      background-color: #999;
      border-radius: 50%;
      flex-shrink: 0;
    }

    .faq-answer {
      padding: 20px 0;
      color: #666;
      line-height: 1.6;
    }

    // 响应式设计
    @include respond-to(md) {
      padding: 40px 20px;

      .title {
        font-size: 2rem;
        margin-bottom: 40px;
      }

      .faq-list {
        max-width: 100%;
      }

      .faq-question {
        font-size: 1rem;
      }
    }
  }
}
</style>
