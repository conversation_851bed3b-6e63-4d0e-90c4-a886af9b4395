<template>
  <div class="news">
    <div class="container mx-auto px-4 py-8">
      <div class="banner-image">
        <img src="/images/news/banner.png" alt="关于赛沃" class="w-full h-auto" />
      </div>
    </div>

    <div class="news-container">
      <div v-loading="loading" class="news-list">
        <div v-if="newsList.length === 0 && !loading" class="list-empty">
          <el-empty description="暂无" />
        </div>

        <div
          v-for="item in newsList"
          :key="item.id"
          class="news-card"
          @click="handleNewsClick(item.id)"
        >
          <div v-if="item.image" class="news-image">
            <img :src="item.image" :alt="item.title" />
          </div>
          <div class="news-content">
            <h3 class="news-title">{{ item.title }}</h3>
            <p class="news-description">{{ item.content }}</p>
            <div class="news-footer">
              <div class="news-meta">
                <span class="news-date">{{ dayjs(item.createdAt).format('YYYY.M.D') }}</span>
                <span class="line">|</span>
                <span class="news-status">{{ item.label }}</span>
              </div>
              <span class="view-details">查看详情</span>
            </div>
          </div>
        </div>
      </div>

      <div v-if="total > 0" class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="prev, pager, next"
          prev-text="上一页"
          next-text="下一页"
          background
          @change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import dayjs from 'dayjs'
import { useLafApi } from '~/composables/useLafApi'

const currentPage = ref(1)
const pageSize = ref(5)

const { getNewsList } = useLafApi()

// 在服务端预获取新闻数据，避免水合不一致问题
const {
  data: newsResponse,
  pending: loading,
  refresh: refreshNews,
} = await useAsyncData(
  'news-list',
  () => getNewsList({ page: currentPage.value, pageSize: pageSize.value }),
  {
    server: true, // 确保服务端执行
    default: () => ({ data: { data: [], meta: { count: 0 } } }), // 提供默认值避免空状态
  }
)

// 从异步数据中提取新闻列表和总数
const newsList = computed(() => {
  return (newsResponse.value as any)?.data?.data || []
})

const newsTotal = computed(() => {
  return (newsResponse.value as any)?.data?.meta?.count || 0
})

// 为了兼容现有的分页逻辑，保持 total 的 ref
const total = computed(() => newsTotal.value)

const getNewsData = async () => {
  await refreshNews()
}

// 如果服务端获取失败，客户端重试
onMounted(async () => {
  if (
    import.meta.client &&
    (!newsResponse.value || !(newsResponse.value as any)?.data?.data?.length)
  ) {
    // 服务端没有数据，客户端重新获取
    await refreshNews()
  }
})

const handleCurrentChange = async (page: number) => {
  currentPage.value = page
  await getNewsData()
}

const handleNewsClick = (newsId: number) => {
  if (import.meta.client) {
    navigateTo(`/about/news-detail/${newsId}`, {
      open: {
        target: '_blank',
      },
    })
  }
}

useHead({
  title: '行业资讯 - 赛沃车联 Sellwell Auto',
  meta: [
    {
      name: 'description',
      content: '赛沃车联行业资讯，提供二手车出口行业最新动态、政策解读、市场分析等专业内容',
    },
    { name: 'keywords', content: '二手车出口,行业资讯,政策解读,市场分析,一带一路' },
  ],
})
</script>

<style lang="scss" scoped>
@use '../../assets/styles/variables' as *;
@use '../../assets/styles/mixins' as *;

.news {
  .banner-image {
    img {
      width: 100%;
    }
  }

  .news-container {
    max-width: $max-width;
    margin: 0 auto;
  }

  .news-list {
    min-height: 520px;
    display: flex;
    flex-direction: column;
    gap: 40px;
    margin: 60px 0 40px 0;
    padding-bottom: 60px;
    border-bottom: 1px solid #dbdbdb;
  }

  .list-empty {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .news-card {
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }
  }

  .news-image {
    flex-shrink: 0;
    width: 300px;
    height: 200px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: fill;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.1);
    }
  }

  .news-content {
    flex: 1;
    padding: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .news-title {
    font-size: 20px;
    font-weight: bold;
    color: #141414;
    margin-bottom: 16px;
    line-height: 1.4;
  }

  .news-description {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 20px;
  }

  .news-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
  }

  .news-meta {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .news-date {
    font-size: 14px;
    color: #999;
  }

  .line {
    font-size: 14px;
    color: #999;
  }

  .news-status {
    font-size: 14px;
    color: #ff6b35;
    font-weight: 500;
  }

  .view-details {
    font-size: 14px;
    color: #666;
    cursor: pointer;
    font-weight: 500;

    &:hover {
      color: #ff6b35;
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    padding-bottom: 60px;

    :deep(.el-pagination) {
      .el-pager li {
        background: transparent;
        color: #666;
        border: none;
        margin: 0 8px;
        min-width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        font-size: 14px;
        transition: color 0.3s ease;

        &:hover {
          color: $primary-color;
        }

        &.is-active {
          color: $primary-color;
          font-weight: bold;
        }
      }

      .btn-prev,
      .btn-next {
        background: transparent;
        color: #666;
        border: none;
        padding: 0 8px;
        height: 24px;
        line-height: 24px;
        font-size: 14px;
        transition: color 0.3s ease;

        &:hover {
          color: $primary-color;
        }

        &:disabled {
          color: #c0c4cc;
          cursor: not-allowed;
        }
      }
    }
  }
}
</style>
