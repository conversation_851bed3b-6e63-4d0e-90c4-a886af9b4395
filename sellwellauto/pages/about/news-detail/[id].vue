<template>
  <div class="news-detail">
    <div class="container mx-auto px-4 py-8">
      <!-- 新闻详情内容 -->
      <div v-loading="loading" class="news-content">
        <div v-if="newsDetail" class="news-article">
          <!-- 新闻标题 -->
          <h1 class="news-title">{{ newsDetail.title }}</h1>

          <!-- 新闻元信息 -->
          <div class="news-meta">
            <span class="news-date">{{ dayjs(newsDetail.createdAt).format('YYYY年M月D日') }}</span>
            <span class="line">|</span>
            <span class="news-status">{{ newsDetail.label }}</span>
          </div>

          <!-- 新闻详细内容 -->
          <div v-if="newsDetail.detail_content" class="news-body">
            <div class="markdown-content" v-html="renderedContent" />
          </div>
        </div>

        <!-- 加载失败或新闻不存在 -->
        <div v-else-if="!loading" class="news-not-found">
          <el-empty description="新闻不存在或已被删除" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { marked } from 'marked'
import dayjs from 'dayjs'
import { useLafApi } from '~/composables/useLafApi'

// 获取路由参数
const route = useRoute()
const newsId = computed(() => Number(route.params.id))

const { getNewsDetail } = useLafApi()

// 获取新闻详情数据
const {
  data: newsResponse,
  pending: loading,
  refresh: refreshNews,
} = await useAsyncData(`news-detail-${newsId.value}`, () => getNewsDetail(newsId.value), {
  server: true,
  default: () => ({ data: null }),
})

// 新闻详情数据
const newsDetail = computed(() => {
  return (newsResponse.value as any)?.data || null
})

// 渲染markdown内容
const renderedContent = computed(() => {
  if (!newsDetail.value?.detail_content) return ''

  try {
    // 配置marked选项
    marked.setOptions({
      breaks: true, // 支持换行
      gfm: true, // 支持GitHub风格markdown
    })

    return marked(newsDetail.value.detail_content)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return newsDetail.value.detail_content
  }
})

// 如果服务端获取失败，客户端重试
onMounted(async () => {
  if (import.meta.client && (!newsResponse.value || !(newsResponse.value as any)?.data)) {
    await refreshNews()
  }
})

// 动态SEO设置
useHead(() => ({
  title: newsDetail.value
    ? `${newsDetail.value.seo_title || newsDetail.value.title}`
    : '赛沃车联新闻详情',
  meta: [
    {
      name: 'description',
      content:
        newsDetail.value?.seo_description || newsDetail.value?.content || '赛沃车联行业资讯详情',
    },
    {
      name: 'keywords',
      content: newsDetail.value?.seo_keywords || '二手车出口,行业资讯,赛沃车联',
    },
  ],
}))
</script>

<style lang="scss" scoped>
@use '../../../assets/styles/variables' as *;
@use '../../../assets/styles/mixins' as *;

.news-detail {
  .container {
    max-width: $max-width;
    margin: 0 auto;
  }

  .news-content {
    min-height: 600px;
  }

  .news-article {
    border-radius: 16px;
    padding: 60px;
  }

  .news-title {
    font-size: 32px;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1.3;
    margin-bottom: 24px;
    text-align: center;
    letter-spacing: -0.02em;
  }

  .news-meta {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  .news-date {
    font-size: 15px;
    color: #666;
    font-weight: 500;
  }

  .line {
    font-size: 15px;
    color: #ddd;
  }

  .news-status {
    font-size: 14px;
    color: #ff6b35;
    font-weight: 600;
    padding: 6px 16px;
    background: rgba(255, 107, 53, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(255, 107, 53, 0.2);
  }

  .news-body {
    .markdown-content {
      font-size: 17px;
      line-height: 1.8;
      color: #2c3e50;
      max-width: 100%;
      overflow-wrap: break-word;
      word-wrap: break-word;

      // 标题样式
      :deep(h1),
      :deep(h2),
      :deep(h3),
      :deep(h4),
      :deep(h5),
      :deep(h6) {
        margin: 32px 0 20px 0;
        font-weight: 700;
        color: #1a1a1a;
        line-height: 1.3;
        letter-spacing: -0.02em;
      }

      :deep(h1) {
        font-size: 28px;
        border-bottom: 3px solid $primary-color;
        padding-bottom: 12px;
      }
      :deep(h2) {
        font-size: 24px;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 8px;
      }
      :deep(h3) {
        font-size: 22px;
        color: #2c3e50;
      }
      :deep(h4) {
        font-size: 20px;
        color: #34495e;
      }
      :deep(h5) {
        font-size: 18px;
        color: #34495e;
      }
      :deep(h6) {
        font-size: 16px;
        color: #34495e;
      }

      // 段落样式
      :deep(p) {
        margin: 20px 0;
        text-align: justify;
        text-justify: inter-word;
        hyphens: auto;
      }

      // 列表样式
      :deep(ul),
      :deep(ol) {
        margin: 20px 0;
        padding-left: 28px;
      }

      :deep(ul li) {
        margin: 12px 0;
        line-height: 1.7;
        position: relative;

        &::before {
          content: '•';
          color: $primary-color;
          font-weight: bold;
          position: absolute;
          left: -20px;
          font-size: 18px;
        }
      }

      :deep(ol li) {
        margin: 12px 0;
        line-height: 1.7;
        counter-increment: list-counter;

        &::marker {
          color: $primary-color;
          font-weight: 600;
        }
      }

      // 链接样式
      :deep(a) {
        color: $primary-color !important;
        text-decoration: none !important;
        border-bottom: 1px solid transparent !important;
        transition: all 0.3s ease !important;
        font-weight: 500 !important;

        &:hover {
          border-bottom-color: $primary-color !important;
          color: color-mix(in srgb, $primary-color 90%, black 10%) !important;
        }
      }

      // 图片样式 - 响应式缩放
      :deep(img) {
        max-width: 100% !important;
        height: auto !important;
        border-radius: 12px !important;
        margin: 24px auto !important;
        display: block !important;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
        transition: all 0.3s ease !important;
        cursor: pointer !important;
      }

      // 视频样式 - 响应式缩放
      :deep(video) {
        max-width: 100% !important;
        height: auto !important;
        border-radius: 12px !important;
        margin: 24px auto !important;
        display: block !important;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
        transition: all 0.3s ease !important;
      }

      // iframe样式 - 响应式缩放
      :deep(iframe) {
        max-width: 100% !important;
        height: auto !important;
        border-radius: 12px !important;
        margin: 24px auto !important;
        display: block !important;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
        transition: all 0.3s ease !important;
      }

      // 引用样式
      :deep(blockquote) {
        margin: 28px 0 !important;
        padding: 20px 24px !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-left: 4px solid $primary-color !important;
        border-radius: 8px !important;
        font-style: italic !important;
        color: #555 !important;
        position: relative !important;

        &::before {
          content: '"' !important;
          font-size: 48px !important;
          color: rgba(255, 107, 53, 0.2) !important;
          position: absolute !important;
          top: -10px !important;
          left: 10px !important;
          font-family: serif !important;
        }

        p {
          margin: 0 !important;
          position: relative !important;
          z-index: 1 !important;
        }
      }

      // 代码样式
      :deep(code) {
        background: #f8f9fa !important;
        padding: 4px 8px !important;
        border-radius: 6px !important;
        font-family:
          'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace !important;
        font-size: 14px !important;
        color: #e83e8c !important;
        border: 1px solid #e9ecef !important;
        font-weight: 500 !important;
      }

      :deep(pre) {
        background: #f8f9fa !important;
        padding: 20px !important;
        border-radius: 12px !important;
        overflow-x: auto !important;
        margin: 24px 0 !important;
        border: 1px solid #e9ecef !important;
        position: relative !important;

        &::before {
          content: '代码' !important;
          position: absolute !important;
          top: 8px !important;
          right: 12px !important;
          font-size: 12px !important;
          color: #6c757d !important;
          background: #e9ecef !important;
          padding: 2px 8px !important;
          border-radius: 10px !important;
          font-weight: 500 !important;
        }

        code {
          background: none !important;
          padding: 0 !important;
          color: #2c3e50 !important;
          border: none !important;
          font-size: 14px !important;
          line-height: 1.6 !important;
        }
      }

      // 表格样式
      :deep(table) {
        width: 100% !important;
        border-collapse: collapse !important;
        margin: 24px 0 !important;
        border-radius: 8px !important;
        overflow: hidden !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;

        th,
        td {
          padding: 16px !important;
          text-align: left !important;
          border-bottom: 1px solid #e9ecef !important;
        }

        th {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
          font-weight: 700 !important;
          color: #1a1a1a !important;
          font-size: 15px !important;
        }

        td {
          font-size: 15px !important;
          color: #2c3e50 !important;
        }

        tr:hover {
          background: rgba(255, 107, 53, 0.02) !important;
        }

        tr:last-child td {
          border-bottom: none !important;
        }
      }

      // 分割线样式
      :deep(hr) {
        border: none !important;
        border-top: 2px solid #e9ecef !important;
        margin: 40px 0 !important;
        position: relative !important;

        &::after {
          content: '◆' !important;
          position: absolute !important;
          top: -8px !important;
          left: 50% !important;
          transform: translateX(-50%) !important;
          background: white !important;
          padding: 0 16px !important;
          color: $primary-color !important;
          font-size: 16px !important;
        }
      }

      // 强调文本
      :deep(strong) {
        color: #1a1a1a !important;
        font-weight: 700 !important;
      }

      :deep(em) {
        color: #555 !important;
        font-style: italic !important;
      }

      // 删除线
      :deep(del) {
        color: #6c757d !important;
        text-decoration: line-through !important;
      }

      // 高亮文本
      :deep(mark) {
        background: rgba(255, 193, 7, 0.3) !important;
        padding: 2px 4px !important;
        border-radius: 4px !important;
        color: #856404 !important;
      }
    }
  }

  .news-not-found {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .news-detail {
    .container {
      max-width: 100%;
      padding: 0 16px;
    }

    .news-article {
      padding: 24px 20px;
      border-radius: 12px;
    }

    .news-title {
      font-size: 26px;
      margin-bottom: 20px;
    }

    .news-meta {
      flex-direction: column;
      gap: 12px;
      margin-bottom: 24px;
    }

    .news-cover {
      margin-bottom: 24px;
    }

    .news-summary {
      margin-bottom: 24px;
      padding: 20px;
    }

    .markdown-content {
      font-size: 16px;

      h1 {
        font-size: 24px;
      }
      h2 {
        font-size: 22px;
      }
      h3 {
        font-size: 20px;
      }

      img,
      video,
      iframe {
        margin: 20px auto;
      }

      table {
        font-size: 14px;

        th,
        td {
          padding: 12px 8px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .news-detail {
    .news-article {
      padding: 20px 16px;
    }

    .news-title {
      font-size: 24px;
    }

    .markdown-content {
      font-size: 15px;

      h1 {
        font-size: 22px;
      }
      h2 {
        font-size: 20px;
      }
      h3 {
        font-size: 18px;
      }

      pre {
        padding: 16px;
        font-size: 13px;
      }

      table {
        font-size: 13px;

        th,
        td {
          padding: 8px 6px;
        }
      }
    }
  }
}
</style>
