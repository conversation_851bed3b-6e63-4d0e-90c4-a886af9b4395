<template>
  <div class="about-sellwellauto">
    <div class="banner-image">
      <img
        src="/images/aboutSellwellauto/banner.png"
        :alt="$t('company.bannerAlt')"
        class="w-full h-auto object-cover"
      />
    </div>

    <div class="sellwellauto-container">
      <h1 class="title">{{ $t('company.title') }}</h1>

      <!-- 公司介绍区域 -->
      <div class="company-intro-section max-container">
        <div class="intro-content">
          <div class="intro-text">
            <h2 class="company-name">{{ $t('company.companyIntro.companyName') }}</h2>
            <div class="red-line" />
            <p class="intro-description">
              {{ $t('company.companyIntro.description') }}
            </p>
          </div>
          <div class="intro-image">
            <img
              src="/images/aboutSellwellauto/mansion.png"
              :alt="$t('company.companyIntro.companyName')"
            />
          </div>
        </div>
      </div>

      <!-- 企业文化区域 -->
      <div class="corporate-culture-section">
        <div class="max-container">
          <h2 class="section-title">{{ $t('company.corporateCulture.title') }}</h2>

          <!-- 第一行：使命和愿景 -->
          <div class="mission-vision-row">
            <div
              v-for="item in corporateCultureData.missionVision"
              :key="item.id"
              class="culture-box"
              :class="`${item.type}-box`"
            >
              <h3 class="culture-title">{{ item.title }}</h3>
              <div class="red-line-small" />
              <p class="culture-description">{{ item.description }}</p>
            </div>
          </div>

          <!-- 第二行：六个核心价值观 -->
          <div class="core-values-row">
            <div
              v-for="item in corporateCultureData.coreValues"
              :key="item.id"
              class="culture-box core-value-box"
              :class="{
                highlighted: highlightedValueId === item.id,
                'default-highlighted': item.highlighted && highlightedValueId === 'global-vision',
              }"
              @mouseenter="handleMouseEnter(item.id)"
            >
              <h3 class="culture-title">{{ item.title }}</h3>
              <div class="red-line-small" />
              <p class="culture-description">{{ item.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 欢迎联系 -->
      <div class="relation-image">
        <img
          src="/images/aboutSellwellauto/relation.png"
          :alt="$t('company.relationImageAlt')"
          class="w-full h-auto object-cover"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n()

// 企业文化数据
const corporateCultureData = computed(() => ({
  // 使命和愿景
  missionVision: [
    {
      id: 'mission',
      title: t('company.corporateCulture.missionVision.mission.title'),
      description: t('company.corporateCulture.missionVision.mission.description'),
      type: 'mission',
    },
    {
      id: 'vision',
      title: t('company.corporateCulture.missionVision.vision.title'),
      description: t('company.corporateCulture.missionVision.vision.description'),
      type: 'vision',
    },
  ],
  // 核心价值观
  coreValues: [
    {
      id: 'global-vision',
      title: t('company.corporateCulture.coreValues.globalVision.title'),
      description: t('company.corporateCulture.coreValues.globalVision.description'),
      highlighted: true,
    },
    {
      id: 'tech-innovation',
      title: t('company.corporateCulture.coreValues.techInnovation.title'),
      description: t('company.corporateCulture.coreValues.techInnovation.description'),
    },
    {
      id: 'equal-opportunity',
      title: t('company.corporateCulture.coreValues.equalOpportunity.title'),
      description: t('company.corporateCulture.coreValues.equalOpportunity.description'),
    },
    {
      id: 'user-first',
      title: t('company.corporateCulture.coreValues.userFirst.title'),
      description: t('company.corporateCulture.coreValues.userFirst.description'),
    },
    {
      id: 'transparent-communication',
      title: t('company.corporateCulture.coreValues.transparentCommunication.title'),
      description: t('company.corporateCulture.coreValues.transparentCommunication.description'),
    },
    {
      id: 'win-win-cooperation',
      title: t('company.corporateCulture.coreValues.winWinCooperation.title'),
      description: t('company.corporateCulture.coreValues.winWinCooperation.description'),
    },
  ],
}))

// 当前高亮的价值观ID
const highlightedValueId = ref('global-vision')

// 鼠标移入处理函数
const handleMouseEnter = (valueId: string) => {
  highlightedValueId.value = valueId
}

useHead({
  title: $t('company.pageTitle'),
  meta: [
    {
      name: 'description',
      content: $t('company.pageDescription'),
    },
    { name: 'keywords', content: $t('company.pageKeywords') },
  ],
})
</script>

<style lang="scss" scoped>
@use '../../assets/styles/variables' as *;
@use '../../assets/styles/mixins' as *;

.about-sellwellauto {
  background-color: #fff;

  .banner-image {
    img {
      width: 100%;
    }
  }

  .max-container {
    max-width: $max-width;
    margin: 0 auto;
  }

  .sellwellauto-container {
    padding: 60px 0px 0px 0px;
  }

  .title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #141414;
    margin-bottom: 60px;
    text-align: center;
  }

  .section-title {
    font-size: 2rem;
    font-weight: bold;
    color: #141414;
    margin-bottom: 40px;
    text-align: center;
  }

  // 公司介绍区域
  .company-intro-section {
    margin-bottom: 80px;

    .intro-content {
      display: flex;
      align-items: center;
      gap: 60px;

      .intro-text {
        flex: 1;

        .company-name {
          font-size: 2rem;
          font-weight: bold;
          color: #616161;
          margin-bottom: 20px;
        }

        .red-line {
          height: 4px;
          width: 20px;
          background-color: $primary-color;
          margin-bottom: 20px;
          border-radius: 4px;
        }

        .intro-description {
          font-size: 1.1rem;
          color: #666;
          line-height: 1.8;
          margin-bottom: 40px;
        }
      }

      .intro-image {
        flex: 1;
        flex-shrink: 0;
        overflow: hidden;
        border-radius: 4px;

        img {
          width: 100%;
          height: auto;
          border-radius: 4px;
          transition: transform 0.3s ease;
          cursor: pointer;

          &:hover {
            transform: scale(1.1);
          }
        }
      }
    }
  }

  // 企业文化区域
  .corporate-culture-section {
    margin-bottom: 80px;
    padding: 60px 0px;
    background-image: url('/images/aboutSellwellauto/enterprise_bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    overflow: hidden; // 防止内容溢出

    .max-container {
      position: relative;
      z-index: 2;
    }

    .mission-vision-row {
      display: flex;
      justify-content: space-around;
      margin-bottom: 40px;
      gap: 20px;
      min-height: 180px; // 设置最小高度，保持布局稳定

      .culture-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: $primary-color;
      }

      .culture-description {
        font-size: 1.2rem;
        color: #666;
        line-height: 1.6;
      }
    }

    .culture-box {
      flex: 1;
      padding: 30px;
      border-radius: 6px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      background-color: rgba(255, 255, 255, 0.4);
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }
    }

    .core-values-row {
      display: flex;
      flex: 1;
      justify-content: space-around;
      flex-wrap: wrap;
      gap: 20px;
      align-items: stretch; // 确保所有方块高度一致

      .culture-box {
        padding: 30px 15px 60px 15px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: transform, box-shadow; // 优化性能
        backface-visibility: hidden; // 防止3D变换导致的抖动
        transform-style: preserve-3d; // 保持3D变换的一致性

        &:hover {
          transform: translateY(-8px) scale(1.02);
        }
      }

      .culture-title {
        font-size: 1.2rem;
        font-weight: bold;
        color: #666;
        transition: color 0.3s ease;
        margin: 0; // 确保标题没有默认边距
      }

      .culture-description {
        font-size: 1rem;
        color: #999;
        transition: color 0.3s ease;
        margin: 0; // 确保描述没有默认边距
        line-height: 1.5; // 固定行高
      }
    }

    .core-value-box {
      &.highlighted {
        background: linear-gradient(135deg, $primary-color 0%, #ff8c42 100%);
        transform: translateY(-8px) scale(1.05);

        .red-line-small {
          background-color: #fff;
          transform: scaleX(1.2);
        }

        .culture-title,
        .culture-description {
          color: #fff;
        }

        .culture-title {
          font-weight: 700;
        }
      }

      &.default-highlighted {
        color: #fff;
        background: linear-gradient(135deg, $primary-color 0%, #ff8c42 100%);

        .red-line-small {
          background-color: #fff;
        }

        .culture-title,
        .culture-description {
          color: #fff;
        }
      }
    }

    .red-line-small {
      height: 3px;
      width: 20px;
      background-color: $primary-color;
      margin: 10px 0px 20px 0px;
      border-radius: 4px;
      transition: all 0.3s ease;
      transform-origin: center;
    }
  }

  .relation-image {
    transform: translateY(5px);
    img {
      width: 100%;
    }
  }

  // 响应式设计
  @include respond-to(md) {
    .sellwellauto-container {
      padding: 40px 20px;
    }

    .title {
      font-size: 2rem;
      margin-bottom: 40px;
    }

    .company-intro-section {
      margin-bottom: 60px;

      .intro-content {
        flex-direction: column;
        gap: 40px;
      }
    }

    .corporate-culture-section {
      margin-bottom: 60px;

      .mission-vision-row {
        flex-direction: column;
        gap: 20px;
      }

      .core-values-row {
        flex-direction: column;
        gap: 20px;
      }
    }
  }
}
</style>
