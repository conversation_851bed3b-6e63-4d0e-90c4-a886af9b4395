<script setup lang="ts">
// 测试页：展示服务器端地区与语言判定结果
import type { RegionApiResponse } from '@/types/region'

const { data } = await useFetch<RegionApiResponse>('/api/region', { method: 'GET' })
const { locale } = useI18n()
</script>

<template>
  <div style="max-width: 720px; margin: 40px auto; line-height: 1.8">
    <h2>/test-region</h2>
    <p>
      当前语言（前端 i18n）: <strong>{{ locale }}</strong>
    </p>

    <div v-if="data?.ok" style="margin-top: 16px">
      <p>
        Cookie 语言: <code>{{ data.cookieLocale }}</code>
      </p>
      <p>
        解析来源: <code>{{ data.region?.reason }}</code>
      </p>
      <p>
        IP: <code>{{ data.region?.ip }}</code>
      </p>
      <p>
        国家: <code>{{ data.region?.country }}</code>
      </p>
      <p>
        国家映射语言: <code>{{ data.region?.localeFromCountry }}</code>
      </p>
      <p>
        UA 推测语言: <code>{{ data.region?.localeFromUA }}</code>
      </p>
      <p>
        最终语言: <code>{{ data.region?.finalLocale }}</code>
      </p>
    </div>

    <div v-else>
      <p>无数据</p>
    </div>
  </div>
</template>

<style scoped>
h2 {
  font-size: 20px;
  margin-bottom: 12px;
}
code {
  padding: 2px 6px;
  background: #f5f5f5;
  border-radius: 4px;
}
</style>
