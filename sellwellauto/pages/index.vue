<template>
  <div class="home-page">
    <HeroSection />

    <!-- 核心功能区域 -->
    <CoreFunctionsSection />

    <!-- 用户成长区域 -->
    <UserGrowthSection />

    <!-- FAQ区域 -->
    <FAQSection />
  </div>
</template>

<script setup lang="ts">
import HeroSection from '@/components/home/<USER>'
import CoreFunctionsSection from '@/components/home/<USER>'
import UserGrowthSection from '@/components/home/<USER>'
import FAQSection from '@/components/home/<USER>'

// SEO 设置
useHead({
  title: '赛沃车联 - 专业二手车出口数字化平台',
  meta: [
    {
      name: 'description',
      content:
        '赛沃车联连接车源方、外贸商、海外买家，提供一站式二手车出口服务。2分钟快速生成报价单，专业培训支持，让二手车出口更简单、更高效。',
    },
    {
      name: 'keywords',
      content: '二手车出口,汽车出口,车源方,外贸商,海外买家,赛沃车联,报价单,一站式服务',
    },
  ],
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
}
</style>
