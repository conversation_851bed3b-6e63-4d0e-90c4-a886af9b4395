<template>
  <div class="usedcar-search-page">
    <div class="container">
      <AdvancedSearch
        @search-result="handleSearchResult"
        @search-error="handleSearchError"
      />

      <div class="button-group">
        <el-button
          :type="carSelectionMode ? 'primary' : 'default'"
          @click="toggleCarSelectionMode"
        >
          {{
            carSelectionMode
              ? $t("usedcarSearch.exitSelection")
              : $t("usedcarSearch.carSelection")
          }}
          <span v-if="selectedCars.length > 0" class="selection-count">
            ({{ selectedCars.length }})
          </span>
        </el-button>

        <el-button v-if="carSelectionMode" @click="share">{{
          $t("usedcarSearch.shareQuote")
        }}</el-button>
      </div>

      <el-empty
        v-if="usedCarList.length === 0"
        :description="$t('usedcarSearch.noData')"
      />
      <UsedcarList
        v-else
        :car-list="usedCarList"
        :car-selection-mode="carSelectionMode"
        @update:selected-cars="handleSelectedCars"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// 车辆数据列表
const usedCarList: any = ref([]);

// 选车模式状态
const carSelectionMode = ref(false);
// 已选中的车辆ID列表
const selectedCars = ref<number[]>([]);

// 切换选车模式
const toggleCarSelectionMode = () => {
  carSelectionMode.value = !carSelectionMode.value;
  if (!carSelectionMode.value) {
    selectedCars.value = [];
  }
};

// 处理选中的车辆数据
const handleSelectedCars = (selectedCarIds: number[]) => {
  selectedCars.value = selectedCarIds;
  console.log("选中的车辆ID:", selectedCarIds);
};

const share = () => {
  const router = useRouter();
  window.open(router.resolve("/usedcar/share").href, "_blank");
};

// 处理搜索结果
const handleSearchResult = (data: { cars: any[]; total: number }) => {
  usedCarList.value = data.cars;
};

// 处理搜索错误
const handleSearchError = (error: string) => {
  ElMessage.error(error);
};
</script>

<style lang="scss" scoped>
@use "../../assets/styles/variables" as *;
@use "../../assets/styles/mixins" as *;

.usedcar-search-page {
  .container {
    max-width: $max-width;
    margin: 0 auto;
  }
}

.button-group {
  padding: 0 24px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;

  .selection-count {
    margin-left: 4px;
    font-weight: 600;
  }
}
</style>
