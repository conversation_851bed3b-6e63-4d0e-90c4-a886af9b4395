// @ts-check
import withNuxt from './.nuxt/eslint.config.mjs'

export default withNuxt(
  // Your custom configs here
  {
    rules: {
      // 允许HTML void元素使用自闭合标签
      'vue/html-self-closing': [
        'error',
        {
          html: {
            void: 'always', // 允许void元素(如img, br, hr等)使用自闭合
            normal: 'always', // 允许普通元素使用自闭合
            component: 'always', // 允许组件使用自闭合
          },
          svg: 'always',
          math: 'always',
        },
      ],
      // 允许使用any类型
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      // 禁止使用v-html指令以防止XSS攻击
      'vue/no-v-html': 'error',
    },
  }
)
